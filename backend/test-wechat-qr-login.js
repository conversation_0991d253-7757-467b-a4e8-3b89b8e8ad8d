const axios = require('axios');
const logger = require('./src/utils/logger');

const baseURL = 'http://localhost:3000/api/v1';

async function testWechatQRLogin() {
  try {
    logger.info('开始测试微信扫码登录功能...');

    // 测试1: 生成微信扫码登录二维码
    logger.info('测试1: 生成微信扫码登录二维码');
    const qrResponse = await axios.post(`${baseURL}/wechat/qr-login`);
    
    if (qrResponse.data.code === 200) {
      const { ticket, qrCode, qrUrl, expiresIn } = qrResponse.data.data;
      logger.info(`✅ 二维码生成成功`);
      logger.info(`票据: ${ticket}`);
      logger.info(`二维码URL: ${qrUrl}`);
      logger.info(`有效期: ${expiresIn}秒`);
      logger.info(`二维码数据长度: ${qrCode.length} 字符`);

      // 测试2: 检查登录状态
      logger.info('测试2: 检查登录状态');
      const statusResponse = await axios.get(`${baseURL}/wechat/qr-login-status?ticket=${ticket}`);
      
      if (statusResponse.data.code === 200) {
        const { status } = statusResponse.data.data;
        logger.info(`✅ 状态检查成功: ${status}`);
        
        if (status === 'waiting') {
          logger.info('✅ 初始状态正确，等待扫码');
        }
      } else {
        logger.error(`❌ 状态检查失败: ${statusResponse.data.message}`);
      }

      // 测试3: 测试无效票据
      logger.info('测试3: 测试无效票据');
      try {
        await axios.get(`${baseURL}/wechat/qr-login-status?ticket=invalid_ticket`);
        logger.error('❌ 应该返回错误，但没有');
      } catch (error) {
        if (error.response && error.response.data.code === 404) {
          logger.info('✅ 无效票据正确返回404错误');
        } else {
          logger.error(`❌ 无效票据返回了意外错误: ${error.message}`);
        }
      }

      // 测试4: 测试缺少票据参数
      logger.info('测试4: 测试缺少票据参数');
      try {
        await axios.get(`${baseURL}/wechat/qr-login-status`);
        logger.error('❌ 应该返回验证错误，但没有');
      } catch (error) {
        if (error.response && error.response.data.code === 400) {
          logger.info('✅ 缺少票据参数正确返回400错误');
        } else {
          logger.error(`❌ 缺少票据参数返回了意外错误: ${error.message}`);
        }
      }

      // 模拟等待一段时间后再次检查状态
      logger.info('等待5秒后再次检查状态...');
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      const statusResponse2 = await axios.get(`${baseURL}/wechat/qr-login-status?ticket=${ticket}`);
      if (statusResponse2.data.code === 200) {
        const { status } = statusResponse2.data.data;
        logger.info(`状态仍为: ${status}`);
      }

      logger.info('🎉 微信扫码登录功能测试完成！');
      logger.info('');
      logger.info('📱 要完成完整的登录流程，请：');
      logger.info('1. 在浏览器中访问: http://localhost:3000/wechat-qr-login.html');
      logger.info('2. 点击"生成登录二维码"按钮');
      logger.info('3. 使用微信扫描二维码');
      logger.info('4. 在微信中确认登录');
      logger.info('');
      logger.info('⚠️  注意：需要配置有效的微信开放平台应用ID和密钥');

    } else {
      logger.error(`❌ 生成二维码失败: ${qrResponse.data.message}`);
    }

  } catch (error) {
    logger.error('❌ 测试失败:', error.response?.data || error.message);
  }
}

// 测试微信扫码登录回调处理
async function testQRLoginCallback() {
  try {
    logger.info('测试微信扫码登录回调处理...');
    
    // 模拟微信回调（这在实际环境中由微信服务器调用）
    const callbackUrl = `${baseURL}/wechat/qr-login-callback?code=mock_code_123&state=mock_ticket_456`;
    
    const response = await axios.get(callbackUrl);
    
    // 由于这是HTML响应，我们检查状态码
    if (response.status === 200) {
      logger.info('✅ 回调接口可以正常访问');
      logger.info('回调返回了HTML页面（用于显示登录结果）');
    }
    
  } catch (error) {
    // 预期会有错误，因为我们使用的是模拟数据
    if (error.response && error.response.status === 200) {
      logger.info('✅ 回调接口正常响应');
    } else {
      logger.info('⚠️  回调接口返回错误（预期的，因为使用了模拟数据）');
    }
  }
}

// 如果直接运行此文件
if (require.main === module) {
  Promise.resolve()
    .then(() => testWechatQRLogin())
    .then(() => testQRLoginCallback())
    .then(() => {
      logger.info('所有测试完成');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('测试失败:', error);
      process.exit(1);
    });
}

module.exports = {
  testWechatQRLogin,
  testQRLoginCallback
};
