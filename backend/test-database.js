const { initDatabase, testConnection } = require('./database/init-database');
const mysql = require('mysql2/promise');
const config = require('./src/config/config');
const logger = require('./src/utils/logger');

async function testDatabaseOperations() {
  let connection;
  
  try {
    logger.info('开始数据库功能测试...');
    
    // 测试连接
    await testConnection();
    
    // 创建连接
    connection = await mysql.createConnection({
      host: config.database.host,
      port: config.database.port,
      user: config.database.username,
      password: config.database.password,
      database: config.database.database,
      charset: 'utf8mb4'
    });
    
    // 测试基本查询
    logger.info('测试基本查询...');
    const [tables] = await connection.query('SHOW TABLES');
    logger.info(`数据库中有 ${tables.length} 个表`);
    
    // 测试分类数据
    logger.info('测试分类数据...');
    const [categories] = await connection.query('SELECT * FROM categories');
    logger.info(`分类表中有 ${categories.length} 条记录`);
    
    // 测试管理员数据
    logger.info('测试管理员数据...');
    const [admins] = await connection.query('SELECT id, username, nickname FROM admins');
    logger.info(`管理员表中有 ${admins.length} 条记录`);
    if (admins.length > 0) {
      logger.info(`默认管理员: ${admins[0].username} (${admins[0].nickname})`);
    }
    
    // 测试积分配置
    logger.info('测试积分配置...');
    const [pointConfigs] = await connection.query('SELECT * FROM point_configs');
    logger.info(`积分配置表中有 ${pointConfigs.length} 条记录`);
    
    // 测试插入操作
    logger.info('测试插入操作...');
    const testUser = {
      nickname: '测试用户',
      points: 100,
      invite_code: 'TEST001'
    };
    
    const [insertResult] = await connection.query(
      'INSERT INTO users (nickname, points, invite_code) VALUES (?, ?, ?)',
      [testUser.nickname, testUser.points, testUser.invite_code]
    );
    
    logger.info(`插入测试用户成功，ID: ${insertResult.insertId}`);
    
    // 测试查询刚插入的数据
    const [testUsers] = await connection.query(
      'SELECT * FROM users WHERE id = ?',
      [insertResult.insertId]
    );
    
    if (testUsers.length > 0) {
      logger.info(`查询测试用户成功: ${testUsers[0].nickname}`);
    }
    
    // 清理测试数据
    await connection.query('DELETE FROM users WHERE id = ?', [insertResult.insertId]);
    logger.info('清理测试数据完成');
    
    logger.info('✅ 数据库功能测试全部通过！');
    
  } catch (error) {
    logger.error('❌ 数据库测试失败:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 如果直接运行此文件
if (require.main === module) {
  testDatabaseOperations().then(() => {
    logger.info('数据库测试完成');
    process.exit(0);
  }).catch((error) => {
    logger.error('数据库测试失败:', error);
    process.exit(1);
  });
}

module.exports = {
  testDatabaseOperations
};
