const axios = require('axios');
const logger = require('./src/utils/logger');

const baseURL = 'http://localhost:3000/api/v1';

// 配置axios以绕过代理
const axiosInstance = axios.create({
  baseURL,
  proxy: false,
  timeout: 10000
});

async function testAdminSystem() {
  try {
    logger.info('开始测试管理后台系统功能...');

    // 测试1: 管理员登录
    logger.info('测试1: 管理员登录');
    let adminToken = null;
    
    try {
      // 测试错误的登录信息
      const wrongLoginResponse = await axiosInstance.post('/admin/login', {
        username: 'admin',
        password: 'wrongpassword'
      });
    } catch (error) {
      if (error.response?.status === 401) {
        logger.info('✅ 错误密码登录被正确拒绝');
      }
    }

    try {
      // 测试正确的登录信息
      const loginResponse = await axiosInstance.post('/admin/login', {
        username: 'admin',
        password: 'admin123',
        rememberMe: true
      });

      if (loginResponse.data.code === 200) {
        adminToken = loginResponse.data.data.token;
        const admin = loginResponse.data.data.admin;
        logger.info(`✅ 管理员登录成功`);
        logger.info(`  - 管理员: ${admin.nickname} (${admin.username})`);
        logger.info(`  - 角色: ${admin.role.name}`);
        logger.info(`  - 令牌: ${adminToken.substring(0, 20)}...`);
        logger.info(`  - 有效期: ${loginResponse.data.data.expiresIn} 秒`);
      }
    } catch (error) {
      logger.error(`❌ 管理员登录失败: ${error.response?.data?.message || error.message}`);
      return;
    }

    // 测试2: 获取管理员信息
    logger.info('测试2: 获取管理员信息');
    try {
      const profileResponse = await axiosInstance.get('/admin/profile', {
        headers: {
          'Authorization': `Bearer ${adminToken}`
        }
      });

      if (profileResponse.data.code === 200) {
        const admin = profileResponse.data.data;
        logger.info(`✅ 获取管理员信息成功`);
        logger.info(`  - 用户名: ${admin.username}`);
        logger.info(`  - 昵称: ${admin.nickname}`);
        logger.info(`  - 邮箱: ${admin.email}`);
        logger.info(`  - 权限: ${admin.role.permissions.join(', ')}`);
      }
    } catch (error) {
      logger.error(`❌ 获取管理员信息失败: ${error.response?.data?.message || error.message}`);
    }

    // 测试3: 获取统计数据
    logger.info('测试3: 获取统计数据');
    try {
      const statsResponse = await axiosInstance.get('/admin/stats', {
        headers: {
          'Authorization': `Bearer ${adminToken}`
        }
      });

      if (statsResponse.data.code === 200) {
        const stats = statsResponse.data.data;
        logger.info(`✅ 获取统计数据成功`);
        logger.info(`  - 总用户数: ${stats.users.total}`);
        logger.info(`  - 活跃用户: ${stats.users.active}`);
        logger.info(`  - 本周新增: ${stats.users.newThisWeek}`);
        logger.info(`  - 总内容数: ${stats.contents.total}`);
        logger.info(`  - 今日浏览: ${stats.contents.todayViews}`);
        logger.info(`  - 总收入: ¥${stats.revenue.total}`);
        logger.info(`  - 本月收入: ¥${stats.revenue.thisMonth}`);
      }
    } catch (error) {
      logger.error(`❌ 获取统计数据失败: ${error.response?.data?.message || error.message}`);
    }

    // 测试4: 管理员内容管理权限
    logger.info('测试4: 管理员内容管理权限');
    try {
      // 创建内容
      const createContentResponse = await axiosInstance.post('/admin/contents', {
        title: '管理员创建的测试内容 - ' + Date.now(),
        summary: '这是管理员通过后台创建的内容',
        content: '管理员创建的详细内容...',
        contentType: 1,
        categoryId: 1,
        unlockType: 1,
        unlockPrice: 50
      }, {
        headers: {
          'Authorization': `Bearer ${adminToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (createContentResponse.data.code === 200) {
        const content = createContentResponse.data.data;
        logger.info(`✅ 管理员创建内容成功，ID: ${content.id}`);

        // 更新内容
        const updateResponse = await axiosInstance.put(`/admin/contents/${content.id}`, {
          title: content.title + ' (已更新)',
          unlockPrice: 100
        }, {
          headers: {
            'Authorization': `Bearer ${adminToken}`,
            'Content-Type': 'application/json'
          }
        });

        if (updateResponse.data.code === 200) {
          logger.info(`✅ 管理员更新内容成功`);
        }

        // 删除内容
        const deleteResponse = await axiosInstance.delete(`/admin/contents/${content.id}`, {
          headers: {
            'Authorization': `Bearer ${adminToken}`
          }
        });

        if (deleteResponse.data.code === 200) {
          logger.info(`✅ 管理员删除内容成功`);
        }
      }
    } catch (error) {
      logger.error(`❌ 管理员内容管理测试失败: ${error.response?.data?.message || error.message}`);
    }

    // 测试5: 管理员分类管理权限
    logger.info('测试5: 管理员分类管理权限');
    try {
      // 创建分类
      const createCategoryResponse = await axiosInstance.post('/admin/categories', {
        name: '管理员测试分类 - ' + Date.now(),
        slug: 'admin-test-' + Date.now(),
        description: '管理员创建的测试分类',
        sortOrder: 99
      }, {
        headers: {
          'Authorization': `Bearer ${adminToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (createCategoryResponse.data.code === 200) {
        const category = createCategoryResponse.data.data;
        logger.info(`✅ 管理员创建分类成功，ID: ${category.id}`);

        // 删除分类
        const deleteResponse = await axiosInstance.delete(`/admin/categories/${category.id}`, {
          headers: {
            'Authorization': `Bearer ${adminToken}`
          }
        });

        if (deleteResponse.data.code === 200) {
          logger.info(`✅ 管理员删除分类成功`);
        }
      }
    } catch (error) {
      logger.error(`❌ 管理员分类管理测试失败: ${error.response?.data?.message || error.message}`);
    }

    // 测试6: 权限验证
    logger.info('测试6: 权限验证');
    try {
      // 测试无效令牌
      const invalidTokenResponse = await axiosInstance.get('/admin/profile', {
        headers: {
          'Authorization': 'Bearer invalid_token'
        }
      });
    } catch (error) {
      if (error.response?.status === 401) {
        logger.info('✅ 无效令牌被正确拒绝');
      }
    }

    try {
      // 测试无令牌访问
      const noTokenResponse = await axiosInstance.get('/admin/profile');
    } catch (error) {
      if (error.response?.status === 401) {
        logger.info('✅ 无令牌访问被正确拒绝');
      }
    }

    logger.info('🎉 管理后台系统功能测试完成！');
    logger.info('');
    logger.info('📊 测试总结:');
    logger.info('✅ 管理员登录认证正常');
    logger.info('✅ 管理员信息获取正常');
    logger.info('✅ 统计数据获取正常');
    logger.info('✅ 内容管理权限正常');
    logger.info('✅ 分类管理权限正常');
    logger.info('✅ 权限验证机制正常');
    logger.info('');
    logger.info('🌐 管理后台访问地址:');
    logger.info('1. 管理员登录: http://localhost:3000/admin-login.html');
    logger.info('2. 管理后台: http://localhost:3000/admin-dashboard.html');
    logger.info('3. 内容管理: http://localhost:3000/content-management.html');
    logger.info('');
    logger.info('🔑 演示账号:');
    logger.info('用户名: admin');
    logger.info('密码: admin123');
    logger.info('');
    logger.info('💡 功能特性:');
    logger.info('- 管理员登录和权限验证');
    logger.info('- 用户管理和统计');
    logger.info('- 内容和分类管理');
    logger.info('- 数据统计和报表');
    logger.info('- 响应式管理界面');

  } catch (error) {
    logger.error('❌ 管理后台系统测试过程中发生错误:', error.message);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  testAdminSystem().then(() => {
    logger.info('管理后台系统测试完成');
    process.exit(0);
  }).catch((error) => {
    logger.error('管理后台系统测试失败:', error);
    process.exit(1);
  });
}

module.exports = {
  testAdminSystem
};
