2025-07-06 13:42:14 [INFO]: 🚀 服务器启动成功！
2025-07-06 13:42:14 [INFO]: 📍 地址: http://0.0.0.0:3000
2025-07-06 13:42:14 [INFO]: 🌍 环境: undefined
2025-07-06 13:42:14 [INFO]: 📚 API文档: http://0.0.0.0:3000/api/v1/docs
2025-07-06 13:42:36 [INFO]: GET / - 200 - 1ms
2025-07-06 13:42:44 [INFO]: GET /api/v1/public/home - 200 - 0ms
2025-07-06 13:46:39 [INFO]: GET /api/v1/public/categories - 200 - 0ms
2025-07-06 13:46:47 [INFO]: GET /api/v1/public/stats - 200 - 0ms
2025-07-06 13:53:37 [INFO]: GET /api/v1/public/config?_t=1751781216767 - 200 - 0ms
2025-07-06 13:53:39 [INFO]: GET /api/v1/public/config?_t=1751781219276 - 200 - 0ms
2025-07-06 13:53:40 [INFO]: GET /api/v1/public/home?_t=1751781220780 - 200 - 0ms
2025-07-06 13:53:40 [INFO]: GET /api/v1/public/stats?_t=1751781220780 - 200 - 0ms
2025-07-06 13:54:43 [INFO]: GET /api/v1/public/home?_t=1751781283090 - 200 - 0ms
2025-07-06 13:54:43 [INFO]: GET /api/v1/public/stats?_t=1751781283090 - 200 - 0ms
2025-07-06 14:14:57 [INFO]: 🚀 服务器启动成功！
2025-07-06 14:14:57 [INFO]: 📍 地址: http://0.0.0.0:3000
2025-07-06 14:14:57 [INFO]: 🌍 环境: undefined
2025-07-06 14:14:57 [INFO]: 📚 API文档: http://0.0.0.0:3000/api/v1/docs
2025-07-06 14:15:18 [INFO]: GET /api/v1/public/home - 200 - 1ms
2025-07-06 14:15:27 [INFO]: GET /api/v1/public/categories - 200 - 0ms
2025-07-06 14:21:58 [INFO]: 🚀 服务器启动成功！
2025-07-06 14:21:58 [INFO]: 📍 地址: http://0.0.0.0:3000
2025-07-06 14:21:58 [INFO]: 🌍 环境: undefined
2025-07-06 14:21:58 [INFO]: 📚 API文档: http://0.0.0.0:3000/api/v1/docs
2025-07-06 15:04:58 [INFO]: 🔗 连接到MySQL服务器...
2025-07-06 15:04:58 [ERROR]: ❌ 数据库初始化失败:
Error: connect ECONNREFUSED 127.0.0.1:3306
    at Object.createConnectionPromise [as createConnection] (/root/zhis/backend/node_modules/mysql2/promise.js:19:31)
    at initDatabase (/root/zhis/backend/database/init-database.js:20:30)
    at Object.<anonymous> (/root/zhis/backend/database/init-database.js:120:3)
    at Module._compile (node:internal/modules/cjs/loader:1364:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)
    at Module.load (node:internal/modules/cjs/loader:1203:32)
    at Module._load (node:internal/modules/cjs/loader:1019:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:128:12)
    at node:internal/main/run_main_module:28:49
2025-07-06 15:06:06 [INFO]: 🔗 连接到MySQL服务器...
2025-07-06 15:06:06 [ERROR]: ❌ 数据库初始化失败:
Error: Access denied for user 'root'@'192.168.31.134' (using password: YES)
    at Object.createConnectionPromise [as createConnection] (/root/zhis/backend/node_modules/mysql2/promise.js:19:31)
    at initDatabase (/root/zhis/backend/database/init-database.js:20:30)
    at Object.<anonymous> (/root/zhis/backend/database/init-database.js:120:3)
    at Module._compile (node:internal/modules/cjs/loader:1364:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)
    at Module.load (node:internal/modules/cjs/loader:1203:32)
    at Module._load (node:internal/modules/cjs/loader:1019:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:128:12)
    at node:internal/main/run_main_module:28:49
