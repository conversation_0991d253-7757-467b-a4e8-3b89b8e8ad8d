2025-07-06 13:42:14 [INFO]: 🚀 服务器启动成功！
2025-07-06 13:42:14 [INFO]: 📍 地址: http://0.0.0.0:3000
2025-07-06 13:42:14 [INFO]: 🌍 环境: undefined
2025-07-06 13:42:14 [INFO]: 📚 API文档: http://0.0.0.0:3000/api/v1/docs
2025-07-06 13:42:36 [INFO]: GET / - 200 - 1ms
2025-07-06 13:42:44 [INFO]: GET /api/v1/public/home - 200 - 0ms
2025-07-06 13:46:39 [INFO]: GET /api/v1/public/categories - 200 - 0ms
2025-07-06 13:46:47 [INFO]: GET /api/v1/public/stats - 200 - 0ms
2025-07-06 13:53:37 [INFO]: GET /api/v1/public/config?_t=1751781216767 - 200 - 0ms
2025-07-06 13:53:39 [INFO]: GET /api/v1/public/config?_t=1751781219276 - 200 - 0ms
2025-07-06 13:53:40 [INFO]: GET /api/v1/public/home?_t=1751781220780 - 200 - 0ms
2025-07-06 13:53:40 [INFO]: GET /api/v1/public/stats?_t=1751781220780 - 200 - 0ms
2025-07-06 13:54:43 [INFO]: GET /api/v1/public/home?_t=1751781283090 - 200 - 0ms
2025-07-06 13:54:43 [INFO]: GET /api/v1/public/stats?_t=1751781283090 - 200 - 0ms
2025-07-06 14:14:57 [INFO]: 🚀 服务器启动成功！
2025-07-06 14:14:57 [INFO]: 📍 地址: http://0.0.0.0:3000
2025-07-06 14:14:57 [INFO]: 🌍 环境: undefined
2025-07-06 14:14:57 [INFO]: 📚 API文档: http://0.0.0.0:3000/api/v1/docs
2025-07-06 14:15:18 [INFO]: GET /api/v1/public/home - 200 - 1ms
2025-07-06 14:15:27 [INFO]: GET /api/v1/public/categories - 200 - 0ms
2025-07-06 14:21:58 [INFO]: 🚀 服务器启动成功！
2025-07-06 14:21:58 [INFO]: 📍 地址: http://0.0.0.0:3000
2025-07-06 14:21:58 [INFO]: 🌍 环境: undefined
2025-07-06 14:21:58 [INFO]: 📚 API文档: http://0.0.0.0:3000/api/v1/docs
2025-07-06 15:04:58 [INFO]: 🔗 连接到MySQL服务器...
2025-07-06 15:04:58 [ERROR]: ❌ 数据库初始化失败:
Error: connect ECONNREFUSED 127.0.0.1:3306
    at Object.createConnectionPromise [as createConnection] (/root/zhis/backend/node_modules/mysql2/promise.js:19:31)
    at initDatabase (/root/zhis/backend/database/init-database.js:20:30)
    at Object.<anonymous> (/root/zhis/backend/database/init-database.js:120:3)
    at Module._compile (node:internal/modules/cjs/loader:1364:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)
    at Module.load (node:internal/modules/cjs/loader:1203:32)
    at Module._load (node:internal/modules/cjs/loader:1019:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:128:12)
    at node:internal/main/run_main_module:28:49
2025-07-06 15:06:06 [INFO]: 🔗 连接到MySQL服务器...
2025-07-06 15:06:06 [ERROR]: ❌ 数据库初始化失败:
Error: Access denied for user 'root'@'192.168.31.134' (using password: YES)
    at Object.createConnectionPromise [as createConnection] (/root/zhis/backend/node_modules/mysql2/promise.js:19:31)
    at initDatabase (/root/zhis/backend/database/init-database.js:20:30)
    at Object.<anonymous> (/root/zhis/backend/database/init-database.js:120:3)
    at Module._compile (node:internal/modules/cjs/loader:1364:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)
    at Module.load (node:internal/modules/cjs/loader:1203:32)
    at Module._load (node:internal/modules/cjs/loader:1019:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:128:12)
    at node:internal/main/run_main_module:28:49
2025-07-06 15:14:16 [INFO]: 🔗 连接到MySQL服务器...
2025-07-06 15:14:16 [ERROR]: ❌ 数据库初始化失败:
Error: Access denied for user 'root'@'localhost'
    at Object.createConnectionPromise [as createConnection] (/root/zhis/backend/node_modules/mysql2/promise.js:19:31)
    at initDatabase (/root/zhis/backend/database/init-database.js:20:30)
    at Object.<anonymous> (/root/zhis/backend/database/init-database.js:120:3)
    at Module._compile (node:internal/modules/cjs/loader:1364:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)
    at Module.load (node:internal/modules/cjs/loader:1203:32)
    at Module._load (node:internal/modules/cjs/loader:1019:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:128:12)
    at node:internal/main/run_main_module:28:49
2025-07-06 15:14:48 [INFO]: 🔗 连接到MySQL服务器...
2025-07-06 15:14:48 [INFO]: 📦 创建数据库...
2025-07-06 15:14:48 [INFO]: ✅ 数据库 zhis 创建成功
2025-07-06 15:14:48 [ERROR]: ❌ 数据库初始化失败:
Error: This command is not supported in the prepared statement protocol yet
    at PromiseConnection.execute (/root/zhis/backend/node_modules/mysql2/lib/promise/connection.js:47:22)
    at initDatabase (/root/zhis/backend/database/init-database.js:29:22)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-07-06 15:15:30 [INFO]: 🔗 连接到MySQL服务器...
2025-07-06 15:15:30 [INFO]: 📦 创建数据库...
2025-07-06 15:15:30 [INFO]: ✅ 数据库 zhis 创建成功
2025-07-06 15:15:30 [ERROR]: ❌ 数据库初始化失败:
Error: This command is not supported in the prepared statement protocol yet
    at PromiseConnection.execute (/root/zhis/backend/node_modules/mysql2/lib/promise/connection.js:47:22)
    at initDatabase (/root/zhis/backend/database/init-database.js:29:22)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-07-06 15:15:52 [INFO]: 🔗 连接到MySQL服务器...
2025-07-06 15:15:52 [INFO]: 📦 创建数据库...
2025-07-06 15:15:52 [INFO]: ✅ 数据库 zhis 创建成功
2025-07-06 15:15:52 [INFO]: 📋 执行数据库架构脚本...
2025-07-06 15:15:55 [INFO]: ✅ 数据库表创建成功
2025-07-06 15:15:55 [INFO]: 🌱 插入初始数据...
2025-07-06 15:15:55 [INFO]: ✅ 初始数据插入成功
2025-07-06 15:16:56 [INFO]: 开始数据库功能测试...
2025-07-06 15:16:56 [INFO]: 测试数据库连接...
2025-07-06 15:16:56 [INFO]: ✅ 数据库连接测试成功
2025-07-06 15:16:56 [INFO]: 测试基本查询...
2025-07-06 15:16:56 [INFO]: 数据库中有 22 个表
2025-07-06 15:16:56 [INFO]: 测试分类数据...
2025-07-06 15:16:56 [INFO]: 分类表中有 6 条记录
2025-07-06 15:16:56 [INFO]: 测试管理员数据...
2025-07-06 15:16:56 [INFO]: 管理员表中有 1 条记录
2025-07-06 15:16:56 [INFO]: 默认管理员: admin (超级管理员)
2025-07-06 15:16:56 [INFO]: 测试积分配置...
2025-07-06 15:16:56 [INFO]: 积分配置表中有 6 条记录
2025-07-06 15:16:56 [INFO]: 测试插入操作...
2025-07-06 15:16:56 [INFO]: 插入测试用户成功，ID: 1
2025-07-06 15:16:56 [INFO]: 查询测试用户成功: 测试用户
2025-07-06 15:16:56 [INFO]: 清理测试数据完成
2025-07-06 15:33:21 [INFO]: 开始测试认证系统...
2025-07-06 15:33:21 [INFO]: 测试1: 创建测试用户
2025-07-06 15:33:21 [ERROR]: ❌ 认证系统测试失败:
Error
    at Query.run (/root/zhis/backend/node_modules/sequelize/lib/dialects/mysql/query.js:52:25)
    at /root/zhis/backend/node_modules/sequelize/lib/sequelize.js:315:28
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async MySQLQueryInterface.insert (/root/zhis/backend/node_modules/sequelize/lib/dialects/abstract/query-interface.js:308:21)
    at async model.save (/root/zhis/backend/node_modules/sequelize/lib/model.js:2490:35)
    at async User.create (/root/zhis/backend/node_modules/sequelize/lib/model.js:1362:12)
    at async testAuthSystem (/root/zhis/backend/test-auth-internal.js:13:22)
2025-07-06 15:35:35 [INFO]: 开始测试认证系统...
2025-07-06 15:35:35 [INFO]: 测试1: 创建测试用户
2025-07-06 15:35:35 [ERROR]: ❌ 认证系统测试失败:
Error
    at Query.run (/root/zhis/backend/node_modules/sequelize/lib/dialects/mysql/query.js:52:25)
    at /root/zhis/backend/node_modules/sequelize/lib/sequelize.js:315:28
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async MySQLQueryInterface.insert (/root/zhis/backend/node_modules/sequelize/lib/dialects/abstract/query-interface.js:308:21)
    at async model.save (/root/zhis/backend/node_modules/sequelize/lib/model.js:2490:35)
    at async User.create (/root/zhis/backend/node_modules/sequelize/lib/model.js:1362:12)
    at async testAuthSystem (/root/zhis/backend/test-auth-internal.js:13:22)
2025-07-06 15:40:32 [INFO]: 🚀 启动微信知识付费平台后端服务...
2025-07-06 15:40:32 [INFO]: 开始初始化数据库...
2025-07-06 15:40:32 [INFO]: 数据库连接成功
2025-07-06 15:40:32 [ERROR]: 数据库同步失败
Error
    at Query.run (/root/zhis/backend/node_modules/sequelize/lib/dialects/mysql/query.js:52:25)
    at /root/zhis/backend/node_modules/sequelize/lib/sequelize.js:315:28
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async MySQLQueryInterface.addIndex (/root/zhis/backend/node_modules/sequelize/lib/dialects/abstract/query-interface.js:250:12)
    at async User.sync (/root/zhis/backend/node_modules/sequelize/lib/model.js:999:7)
    at async Sequelize.sync (/root/zhis/backend/node_modules/sequelize/lib/sequelize.js:377:9)
    at async syncDatabase (/root/zhis/backend/src/models/index.js:176:5)
    at async initDatabase (/root/zhis/backend/database/init.js:14:5)
    at async start (/root/zhis/backend/scripts/start.js:9:5)
2025-07-06 15:41:09 [INFO]: 🚀 启动微信知识付费平台后端服务...
2025-07-06 15:41:09 [INFO]: 开始初始化数据库...
2025-07-06 15:41:09 [INFO]: 数据库连接成功
2025-07-06 15:41:10 [ERROR]: 数据库同步失败
Error
    at Query.run (/root/zhis/backend/node_modules/sequelize/lib/dialects/mysql/query.js:52:25)
    at /root/zhis/backend/node_modules/sequelize/lib/sequelize.js:315:28
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async MySQLQueryInterface.addIndex (/root/zhis/backend/node_modules/sequelize/lib/dialects/abstract/query-interface.js:250:12)
    at async User.sync (/root/zhis/backend/node_modules/sequelize/lib/model.js:999:7)
    at async Sequelize.sync (/root/zhis/backend/node_modules/sequelize/lib/sequelize.js:377:9)
    at async syncDatabase (/root/zhis/backend/src/models/index.js:176:5)
    at async initDatabase (/root/zhis/backend/database/init.js:14:5)
    at async start (/root/zhis/backend/scripts/start.js:9:5)
2025-07-06 15:42:43 [INFO]: 🚀 启动微信知识付费平台后端服务...
2025-07-06 15:42:43 [INFO]: 开始初始化数据库...
2025-07-06 15:42:43 [INFO]: 数据库连接成功
2025-07-06 15:42:44 [ERROR]: 数据库同步失败
Error
    at Query.run (/root/zhis/backend/node_modules/sequelize/lib/dialects/mysql/query.js:52:25)
    at /root/zhis/backend/node_modules/sequelize/lib/sequelize.js:315:28
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async MySQLQueryInterface.addIndex (/root/zhis/backend/node_modules/sequelize/lib/dialects/abstract/query-interface.js:250:12)
    at async Content.sync (/root/zhis/backend/node_modules/sequelize/lib/model.js:999:7)
    at async Sequelize.sync (/root/zhis/backend/node_modules/sequelize/lib/sequelize.js:377:9)
    at async syncDatabase (/root/zhis/backend/src/models/index.js:176:5)
    at async initDatabase (/root/zhis/backend/database/init.js:14:5)
    at async start (/root/zhis/backend/scripts/start.js:9:5)
2025-07-06 15:45:08 [INFO]: 🚀 测试服务器启动成功，端口: 3000
2025-07-06 15:45:08 [INFO]: 📱 微信扫码登录演示页面: http://localhost:3000/wechat-qr-login.html
2025-07-06 15:45:31 [INFO]: 开始测试微信扫码登录功能...
2025-07-06 15:45:31 [INFO]: 测试1: 生成微信扫码登录二维码
2025-07-06 15:45:34 [ERROR]: ❌ 测试失败:
2025-07-06 15:45:34 [INFO]: 测试微信扫码登录回调处理...
2025-07-06 15:45:37 [INFO]: ⚠️  回调接口返回错误（预期的，因为使用了模拟数据）
2025-07-06 15:45:50 [INFO]: 生成微信扫码登录二维码
