2025-07-06 15:04:58 [ERROR]: ❌ 数据库初始化失败:
Error: connect ECONNREFUSED 127.0.0.1:3306
    at Object.createConnectionPromise [as createConnection] (/root/zhis/backend/node_modules/mysql2/promise.js:19:31)
    at initDatabase (/root/zhis/backend/database/init-database.js:20:30)
    at Object.<anonymous> (/root/zhis/backend/database/init-database.js:120:3)
    at Module._compile (node:internal/modules/cjs/loader:1364:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)
    at Module.load (node:internal/modules/cjs/loader:1203:32)
    at Module._load (node:internal/modules/cjs/loader:1019:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:128:12)
    at node:internal/main/run_main_module:28:49
2025-07-06 15:06:06 [ERROR]: ❌ 数据库初始化失败:
Error: Access denied for user 'root'@'192.168.31.134' (using password: YES)
    at Object.createConnectionPromise [as createConnection] (/root/zhis/backend/node_modules/mysql2/promise.js:19:31)
    at initDatabase (/root/zhis/backend/database/init-database.js:20:30)
    at Object.<anonymous> (/root/zhis/backend/database/init-database.js:120:3)
    at Module._compile (node:internal/modules/cjs/loader:1364:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)
    at Module.load (node:internal/modules/cjs/loader:1203:32)
    at Module._load (node:internal/modules/cjs/loader:1019:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:128:12)
    at node:internal/main/run_main_module:28:49
2025-07-06 15:14:16 [ERROR]: ❌ 数据库初始化失败:
Error: Access denied for user 'root'@'localhost'
    at Object.createConnectionPromise [as createConnection] (/root/zhis/backend/node_modules/mysql2/promise.js:19:31)
    at initDatabase (/root/zhis/backend/database/init-database.js:20:30)
    at Object.<anonymous> (/root/zhis/backend/database/init-database.js:120:3)
    at Module._compile (node:internal/modules/cjs/loader:1364:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1422:10)
    at Module.load (node:internal/modules/cjs/loader:1203:32)
    at Module._load (node:internal/modules/cjs/loader:1019:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:128:12)
    at node:internal/main/run_main_module:28:49
2025-07-06 15:14:48 [ERROR]: ❌ 数据库初始化失败:
Error: This command is not supported in the prepared statement protocol yet
    at PromiseConnection.execute (/root/zhis/backend/node_modules/mysql2/lib/promise/connection.js:47:22)
    at initDatabase (/root/zhis/backend/database/init-database.js:29:22)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-07-06 15:15:30 [ERROR]: ❌ 数据库初始化失败:
Error: This command is not supported in the prepared statement protocol yet
    at PromiseConnection.execute (/root/zhis/backend/node_modules/mysql2/lib/promise/connection.js:47:22)
    at initDatabase (/root/zhis/backend/database/init-database.js:29:22)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-07-06 15:33:21 [ERROR]: ❌ 认证系统测试失败:
Error
    at Query.run (/root/zhis/backend/node_modules/sequelize/lib/dialects/mysql/query.js:52:25)
    at /root/zhis/backend/node_modules/sequelize/lib/sequelize.js:315:28
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async MySQLQueryInterface.insert (/root/zhis/backend/node_modules/sequelize/lib/dialects/abstract/query-interface.js:308:21)
    at async model.save (/root/zhis/backend/node_modules/sequelize/lib/model.js:2490:35)
    at async User.create (/root/zhis/backend/node_modules/sequelize/lib/model.js:1362:12)
    at async testAuthSystem (/root/zhis/backend/test-auth-internal.js:13:22)
2025-07-06 15:35:35 [ERROR]: ❌ 认证系统测试失败:
Error
    at Query.run (/root/zhis/backend/node_modules/sequelize/lib/dialects/mysql/query.js:52:25)
    at /root/zhis/backend/node_modules/sequelize/lib/sequelize.js:315:28
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async MySQLQueryInterface.insert (/root/zhis/backend/node_modules/sequelize/lib/dialects/abstract/query-interface.js:308:21)
    at async model.save (/root/zhis/backend/node_modules/sequelize/lib/model.js:2490:35)
    at async User.create (/root/zhis/backend/node_modules/sequelize/lib/model.js:1362:12)
    at async testAuthSystem (/root/zhis/backend/test-auth-internal.js:13:22)
2025-07-06 15:40:32 [ERROR]: 数据库同步失败
Error
    at Query.run (/root/zhis/backend/node_modules/sequelize/lib/dialects/mysql/query.js:52:25)
    at /root/zhis/backend/node_modules/sequelize/lib/sequelize.js:315:28
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async MySQLQueryInterface.addIndex (/root/zhis/backend/node_modules/sequelize/lib/dialects/abstract/query-interface.js:250:12)
    at async User.sync (/root/zhis/backend/node_modules/sequelize/lib/model.js:999:7)
    at async Sequelize.sync (/root/zhis/backend/node_modules/sequelize/lib/sequelize.js:377:9)
    at async syncDatabase (/root/zhis/backend/src/models/index.js:176:5)
    at async initDatabase (/root/zhis/backend/database/init.js:14:5)
    at async start (/root/zhis/backend/scripts/start.js:9:5)
2025-07-06 15:41:10 [ERROR]: 数据库同步失败
Error
    at Query.run (/root/zhis/backend/node_modules/sequelize/lib/dialects/mysql/query.js:52:25)
    at /root/zhis/backend/node_modules/sequelize/lib/sequelize.js:315:28
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async MySQLQueryInterface.addIndex (/root/zhis/backend/node_modules/sequelize/lib/dialects/abstract/query-interface.js:250:12)
    at async User.sync (/root/zhis/backend/node_modules/sequelize/lib/model.js:999:7)
    at async Sequelize.sync (/root/zhis/backend/node_modules/sequelize/lib/sequelize.js:377:9)
    at async syncDatabase (/root/zhis/backend/src/models/index.js:176:5)
    at async initDatabase (/root/zhis/backend/database/init.js:14:5)
    at async start (/root/zhis/backend/scripts/start.js:9:5)
2025-07-06 15:42:44 [ERROR]: 数据库同步失败
Error
    at Query.run (/root/zhis/backend/node_modules/sequelize/lib/dialects/mysql/query.js:52:25)
    at /root/zhis/backend/node_modules/sequelize/lib/sequelize.js:315:28
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async MySQLQueryInterface.addIndex (/root/zhis/backend/node_modules/sequelize/lib/dialects/abstract/query-interface.js:250:12)
    at async Content.sync (/root/zhis/backend/node_modules/sequelize/lib/model.js:999:7)
    at async Sequelize.sync (/root/zhis/backend/node_modules/sequelize/lib/sequelize.js:377:9)
    at async syncDatabase (/root/zhis/backend/src/models/index.js:176:5)
    at async initDatabase (/root/zhis/backend/database/init.js:14:5)
    at async start (/root/zhis/backend/scripts/start.js:9:5)
2025-07-06 15:45:34 [ERROR]: ❌ 测试失败:
2025-07-06 16:00:14 [ERROR]: ❌ 获取分类列表失败: Request failed with status code 502
2025-07-06 16:00:16 [ERROR]: ❌ 获取内容列表失败: Request failed with status code 502
2025-07-06 16:00:18 [ERROR]: ❌ 搜索功能失败: Request failed with status code 502
2025-07-06 16:00:21 [ERROR]: ❌ 创建内容失败: Request failed with status code 502
2025-07-06 16:00:25 [ERROR]: ❌ 分类管理测试失败: Request failed with status code 502
2025-07-06 16:01:11 [ERROR]: ❌ 获取分类列表失败: Request failed with status code 502
2025-07-06 16:01:13 [ERROR]: ❌ 获取内容列表失败: Request failed with status code 502
2025-07-06 16:01:16 [ERROR]: ❌ 搜索功能失败: Request failed with status code 502
2025-07-06 16:01:19 [ERROR]: ❌ 创建内容失败: Request failed with status code 502
2025-07-06 16:01:22 [ERROR]: ❌ 分类管理测试失败: Request failed with status code 502
2025-07-06 16:03:02 [ERROR]: ❌ 获取分类列表失败: Request failed with status code 502
2025-07-06 16:03:05 [ERROR]: ❌ 获取内容列表失败: Request failed with status code 502
2025-07-06 16:03:08 [ERROR]: ❌ 搜索功能失败: Request failed with status code 502
2025-07-06 16:03:11 [ERROR]: ❌ 创建内容失败: Request failed with status code 502
2025-07-06 16:03:13 [ERROR]: ❌ 分类管理测试失败: Request failed with status code 502
2025-07-06 16:35:15 [ERROR]: ❌ 微信扫码登录 HTML验证失败: Request failed with status code 502
2025-07-06 16:35:18 [ERROR]: ❌ 内容管理演示 HTML验证失败: Request failed with status code 502
2025-07-06 16:35:21 [ERROR]: ❌ 管理员登录 HTML验证失败: Request failed with status code 502
2025-07-06 16:35:24 [ERROR]: ❌ 管理后台 HTML验证失败: Request failed with status code 502
2025-07-06 16:35:26 [ERROR]: ❌ 积分卡密管理 HTML验证失败: Request failed with status code 502
2025-07-06 16:35:28 [ERROR]: ❌ 微信扫码登录 CSS验证失败: Request failed with status code 502
2025-07-06 16:35:30 [ERROR]: ❌ 管理员登录 CSS验证失败: Request failed with status code 502
2025-07-06 16:35:34 [ERROR]: ❌ 管理后台 CSS验证失败: Request failed with status code 502
2025-07-06 16:35:37 [ERROR]: ❌ 微信扫码登录 JavaScript验证失败: Request failed with status code 502
2025-07-06 16:35:43 [ERROR]: ❌ 内容管理演示 JavaScript验证失败: Request failed with status code 502
2025-07-06 16:35:45 [ERROR]: ❌ 管理员登录 JavaScript验证失败: Request failed with status code 502
2025-07-06 16:35:48 [ERROR]: ❌ 管理后台 JavaScript验证失败: Request failed with status code 502
2025-07-06 16:35:51 [ERROR]: ❌ 积分卡密管理 JavaScript验证失败: Request failed with status code 502
2025-07-06 16:35:54 [ERROR]: ❌ 微信扫码登录 可访问性验证失败: Request failed with status code 502
2025-07-06 16:35:57 [ERROR]: ❌ 管理员登录 可访问性验证失败: Request failed with status code 502
2025-07-06 16:36:00 [ERROR]: ❌ 微信扫码登录 性能验证失败: Request failed with status code 502
2025-07-06 16:36:03 [ERROR]: ❌ 管理后台 性能验证失败: Request failed with status code 502
2025-07-06 16:50:52 [ERROR]: ❌ 服务器未启动或无法访问
2025-07-06 16:50:52 [ERROR]:    错误: Request failed with status code 404
2025-07-06 16:50:53 [ERROR]: ❌ 部署验证失败 - 存在问题
