-- 知识付费平台数据库初始化脚本
-- 数据库: zhis
-- 服务器: 192.168.31.91:3306
-- 用户: root
-- 密码: mysql_jZKFP62

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS zhis DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE zhis;

-- 1. 创建用户表
CREATE TABLE IF NOT EXISTS users (
  id INT PRIMARY KEY AUTO_INCREMENT,
  openid VARCHAR(100) UNIQUE NOT NULL COMMENT '微信openid',
  nickname VARCHAR(100) COMMENT '用户昵称',
  avatar VARCHAR(255) COMMENT '头像URL',
  phone VARCHAR(20) COMMENT '手机号',
  points INT DEFAULT 0 COMMENT '积分余额',
  vip_level INT DEFAULT 0 COMMENT 'VIP等级',
  status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 2. 创建管理员表
CREATE TABLE IF NOT EXISTS admins (
  id INT PRIMARY KEY AUTO_INCREMENT,
  username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
  password VARCHAR(255) NOT NULL COMMENT '密码（加密）',
  nickname VARCHAR(100) COMMENT '昵称',
  email VARCHAR(100) COMMENT '邮箱',
  role_id INT DEFAULT 1 COMMENT '角色ID',
  status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
  last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员表';

-- 3. 创建角色表
CREATE TABLE IF NOT EXISTS roles (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(50) NOT NULL COMMENT '角色名称',
  description VARCHAR(255) COMMENT '角色描述',
  permissions JSON COMMENT '权限列表',
  status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';

-- 4. 创建分类表
CREATE TABLE IF NOT EXISTS categories (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL COMMENT '分类名称',
  slug VARCHAR(100) UNIQUE NOT NULL COMMENT '分类标识',
  description TEXT COMMENT '分类描述',
  sort_order INT DEFAULT 0 COMMENT '排序',
  status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分类表';

-- 5. 创建内容表
CREATE TABLE IF NOT EXISTS contents (
  id INT PRIMARY KEY AUTO_INCREMENT,
  title VARCHAR(255) NOT NULL COMMENT '标题',
  summary TEXT COMMENT '摘要',
  content LONGTEXT COMMENT '内容',
  content_type TINYINT DEFAULT 1 COMMENT '内容类型：1-文章，2-网盘，3-视频，4-文档',
  category_id INT COMMENT '分类ID',
  unlock_type TINYINT DEFAULT 1 COMMENT '解锁类型：1-积分，2-VIP，3-卡密，4-免费',
  unlock_price INT DEFAULT 0 COMMENT '解锁价格',
  view_count INT DEFAULT 0 COMMENT '浏览次数',
  like_count INT DEFAULT 0 COMMENT '点赞次数',
  unlock_count INT DEFAULT 0 COMMENT '解锁次数',
  is_recommend TINYINT DEFAULT 0 COMMENT '是否推荐',
  is_hot TINYINT DEFAULT 0 COMMENT '是否热门',
  status TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
  created_by INT COMMENT '创建者ID',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX idx_category_id (category_id),
  INDEX idx_content_type (content_type),
  INDEX idx_unlock_type (unlock_type),
  INDEX idx_status (status),
  INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='内容表';

-- 6. 创建内容解锁记录表
CREATE TABLE IF NOT EXISTS content_unlocks (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL COMMENT '用户ID',
  content_id INT NOT NULL COMMENT '内容ID',
  unlock_type TINYINT NOT NULL COMMENT '解锁类型',
  unlock_price INT DEFAULT 0 COMMENT '解锁价格',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '解锁时间',
  UNIQUE KEY unique_user_content (user_id, content_id),
  INDEX idx_user_id (user_id),
  INDEX idx_content_id (content_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='内容解锁记录表';

-- 7. 创建积分配置表
CREATE TABLE IF NOT EXISTS point_configs (
  id INT PRIMARY KEY AUTO_INCREMENT,
  type VARCHAR(50) UNIQUE NOT NULL COMMENT '配置类型',
  points INT NOT NULL COMMENT '积分数量',
  description VARCHAR(255) COMMENT '描述',
  is_enabled TINYINT DEFAULT 1 COMMENT '是否启用',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='积分配置表';

-- 8. 创建卡密表
CREATE TABLE IF NOT EXISTS card_codes (
  id INT PRIMARY KEY AUTO_INCREMENT,
  code VARCHAR(50) UNIQUE NOT NULL COMMENT '卡密代码',
  type TINYINT NOT NULL COMMENT '卡密类型：1-积分卡，2-VIP卡',
  value INT DEFAULT 0 COMMENT '卡密价值',
  description VARCHAR(255) COMMENT '描述',
  status TINYINT DEFAULT 1 COMMENT '状态：1-未使用，2-已使用，0-已禁用',
  expires_at TIMESTAMP NULL COMMENT '过期时间',
  created_by INT COMMENT '创建者ID',
  used_by INT NULL COMMENT '使用者ID',
  used_at TIMESTAMP NULL COMMENT '使用时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX idx_code (code),
  INDEX idx_type (type),
  INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='卡密表';

-- 9. 创建积分日志表
CREATE TABLE IF NOT EXISTS point_logs (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL COMMENT '用户ID',
  points INT NOT NULL COMMENT '积分变动（正数为增加，负数为减少）',
  type VARCHAR(50) NOT NULL COMMENT '变动类型',
  description VARCHAR(255) COMMENT '描述',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  INDEX idx_user_id (user_id),
  INDEX idx_type (type),
  INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='积分日志表';

-- 10. 创建微信登录票据表
CREATE TABLE IF NOT EXISTS wechat_login_tickets (
  id INT PRIMARY KEY AUTO_INCREMENT,
  ticket VARCHAR(100) UNIQUE NOT NULL COMMENT '登录票据',
  status VARCHAR(20) DEFAULT 'waiting' COMMENT '状态：waiting-等待，scanned-已扫码，confirmed-已确认，expired-已过期',
  user_id INT NULL COMMENT '用户ID',
  expires_at TIMESTAMP NOT NULL COMMENT '过期时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX idx_ticket (ticket),
  INDEX idx_status (status),
  INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信登录票据表';

-- 插入初始数据

-- 插入角色数据
INSERT IGNORE INTO roles (id, name, description, permissions) VALUES
(1, '超级管理员', '拥有所有权限', '["*"]'),
(2, '内容管理员', '管理内容和分类', '["content.*", "category.*"]'),
(3, '用户管理员', '管理用户信息', '["user.*"]');

-- 插入管理员数据（密码：admin123，已加密）
INSERT IGNORE INTO admins (id, username, password, nickname, role_id) VALUES
(1, 'admin', '$2b$10$rQZ8kHWKtLrMQxOcKJFqKOYrS8WxUxN5B5qJ5J5J5J5J5J5J5J5J5O', '系统管理员', 1);

-- 插入分类数据
INSERT IGNORE INTO categories (id, name, slug, description, sort_order) VALUES
(1, '技术文章', 'tech', '技术相关的文章和教程', 1),
(2, '生活分享', 'life', '生活经验和心得分享', 2),
(3, '学习资源', 'study', '各类学习资料和资源', 3);

-- 插入示例内容
INSERT IGNORE INTO contents (id, title, summary, content, content_type, category_id, unlock_type, unlock_price, view_count) VALUES
(1, '示例文章1', '这是一个示例文章的摘要', '这里是文章的详细内容...', 1, 1, 4, 0, 100),
(2, '示例文章2', '这是另一个示例文章', '这里是另一篇文章的内容...', 1, 2, 1, 50, 80);

-- 插入积分配置
INSERT IGNORE INTO point_configs (type, points, description) VALUES
('daily_signin', 10, '每日签到奖励'),
('watch_ad', 5, '观看广告奖励'),
('invite_user', 50, '邀请用户奖励'),
('first_share', 20, '首次分享奖励'),
('complete_profile', 30, '完善资料奖励');

-- 插入示例卡密
INSERT IGNORE INTO card_codes (code, type, value, description, created_by) VALUES
('DEMO2024001', 1, 100, '演示积分卡', 1),
('VIP2024001', 2, 30, '30天VIP卡', 1);

-- 创建外键约束
ALTER TABLE contents ADD CONSTRAINT fk_contents_category FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL;
ALTER TABLE content_unlocks ADD CONSTRAINT fk_unlocks_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
ALTER TABLE content_unlocks ADD CONSTRAINT fk_unlocks_content FOREIGN KEY (content_id) REFERENCES contents(id) ON DELETE CASCADE;
ALTER TABLE point_logs ADD CONSTRAINT fk_point_logs_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
ALTER TABLE wechat_login_tickets ADD CONSTRAINT fk_tickets_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL;

-- 创建视图：内容统计
CREATE OR REPLACE VIEW content_stats AS
SELECT 
  c.id,
  c.title,
  c.view_count,
  c.like_count,
  c.unlock_count,
  cat.name as category_name,
  CASE c.content_type
    WHEN 1 THEN '文章'
    WHEN 2 THEN '网盘'
    WHEN 3 THEN '视频'
    WHEN 4 THEN '文档'
    ELSE '未知'
  END as content_type_text,
  CASE c.unlock_type
    WHEN 1 THEN '积分'
    WHEN 2 THEN 'VIP'
    WHEN 3 THEN '卡密'
    WHEN 4 THEN '免费'
    ELSE '未知'
  END as unlock_type_text,
  c.created_at
FROM contents c
LEFT JOIN categories cat ON c.category_id = cat.id
WHERE c.status = 1;

-- 创建视图：用户统计
CREATE OR REPLACE VIEW user_stats AS
SELECT 
  COUNT(*) as total_users,
  COUNT(CASE WHEN status = 1 THEN 1 END) as active_users,
  COUNT(CASE WHEN vip_level > 0 THEN 1 END) as vip_users,
  AVG(points) as avg_points,
  SUM(points) as total_points
FROM users;

-- 创建存储过程：清理过期票据
DELIMITER //
CREATE PROCEDURE CleanExpiredTickets()
BEGIN
  DELETE FROM wechat_login_tickets 
  WHERE expires_at < NOW() AND status IN ('waiting', 'scanned');
END //
DELIMITER ;

-- 创建定时任务清理过期票据（需要开启事件调度器）
-- SET GLOBAL event_scheduler = ON;
-- CREATE EVENT IF NOT EXISTS clean_expired_tickets
-- ON SCHEDULE EVERY 1 HOUR
-- DO CALL CleanExpiredTickets();

COMMIT;

-- 显示创建结果
SELECT 'Database setup completed successfully!' as message;
SHOW TABLES;
