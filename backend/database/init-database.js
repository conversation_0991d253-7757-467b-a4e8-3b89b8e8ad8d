const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
const appConfig = require('../src/config/config');
const logger = require('../src/utils/logger');

const config = {
  host: appConfig.database.host,
  port: appConfig.database.port,
  user: appConfig.database.username,
  password: appConfig.database.password,
  multipleStatements: true
};

async function initDatabase() {
  let connection;

  try {
    logger.info('🔗 连接到MySQL服务器...');
    connection = await mysql.createConnection(config);

    // 创建数据库
    logger.info('📦 创建数据库...');
    const dbName = appConfig.database.database;
    await connection.execute(`CREATE DATABASE IF NOT EXISTS \`${dbName}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
    logger.info(`✅ 数据库 ${dbName} 创建成功`);

    // 选择数据库
    await connection.query(`USE \`${dbName}\``);

    // 读取并执行SQL文件
    const sqlFile = path.join(__dirname, 'schema.sql');
    if (fs.existsSync(sqlFile)) {
      logger.info('📋 执行数据库架构脚本...');
      const sql = fs.readFileSync(sqlFile, 'utf8');

      // 分割SQL语句并逐个执行
      const statements = sql.split(';').filter(stmt => stmt.trim());
      for (const statement of statements) {
        if (statement.trim()) {
          await connection.query(statement);
        }
      }
      logger.info('✅ 数据库表创建成功');
    }

    // 执行初始数据脚本
    const seedFile = path.join(__dirname, 'seeds.sql');
    if (fs.existsSync(seedFile)) {
      logger.info('🌱 插入初始数据...');
      const seedSql = fs.readFileSync(seedFile, 'utf8');

      // 分割SQL语句并逐个执行
      const seedStatements = seedSql.split(';').filter(stmt => stmt.trim());
      for (const statement of seedStatements) {
        if (statement.trim()) {
          try {
            await connection.query(statement);
          } catch (error) {
            // 忽略重复插入错误
            if (!error.message.includes('Duplicate entry')) {
              throw error;
            }
          }
        }
      }
      logger.info('✅ 初始数据插入成功');
    }

    logger.info('🎉 数据库初始化完成！');

  } catch (error) {
    logger.error('❌ 数据库初始化失败:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 测试数据库连接
async function testConnection() {
  let connection;

  try {
    logger.info('测试数据库连接...');

    connection = await mysql.createConnection({
      host: appConfig.database.host,
      port: appConfig.database.port,
      user: appConfig.database.username,
      password: appConfig.database.password,
      database: appConfig.database.database,
      charset: 'utf8mb4'
    });

    // 执行简单查询测试
    const [rows] = await connection.execute('SELECT 1 as test');

    if (rows[0].test === 1) {
      logger.info('✅ 数据库连接测试成功');
      return true;
    } else {
      throw new Error('数据库连接测试失败');
    }

  } catch (error) {
    logger.error('❌ 数据库连接测试失败:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 如果直接运行此文件
if (require.main === module) {
  initDatabase().then(() => {
    logger.info('数据库初始化完成，程序退出');
    process.exit(0);
  }).catch((error) => {
    logger.error('数据库初始化失败:', error);
    process.exit(1);
  });
}

module.exports = {
  initDatabase,
  testConnection
};
