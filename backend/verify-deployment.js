const axios = require('axios');
const logger = require('./src/utils/logger');

const baseURL = 'http://localhost:3000';

// 配置axios以绕过代理
const webAxios = axios.create({
  baseURL,
  proxy: false,
  timeout: 10000
});

const apiAxios = axios.create({
  baseURL: baseURL + '/api/v1',
  proxy: false,
  timeout: 10000
});

async function verifyDeployment() {
  logger.info('🔍 开始验证部署状态...');
  logger.info('');

  const results = {
    server: false,
    database: false,
    frontend: false,
    backend: false,
    admin: false,
    overall: false
  };

  try {
    // 1. 验证服务器启动
    logger.info('📡 验证服务器启动状态...');
    try {
      // 尝试访问一个已知存在的页面
      const response = await webAxios.get('/admin-login.html');
      if (response.status === 200) {
        results.server = true;
        logger.info('✅ 服务器启动正常');
      }
    } catch (error) {
      logger.error('❌ 服务器未启动或无法访问');
      logger.error(`   错误: ${error.message}`);
    }

    // 2. 验证数据库连接
    logger.info('🗄️ 验证数据库连接...');
    try {
      const response = await apiAxios.get('/categories');
      if (response.data.code === 200) {
        results.database = true;
        logger.info('✅ 数据库连接正常');
        logger.info(`   获取到 ${response.data.data.length} 个分类`);
      }
    } catch (error) {
      logger.error('❌ 数据库连接失败');
      logger.error(`   错误: ${error.response?.data?.message || error.message}`);
    }

    // 3. 验证前端页面
    logger.info('🎨 验证前端页面...');
    const pages = [
      { name: '微信扫码登录', url: '/wechat-qr-login.html' },
      { name: '内容管理演示', url: '/content-management.html' },
      { name: '管理员登录', url: '/admin-login.html' },
      { name: '管理后台', url: '/admin-dashboard.html' },
      { name: '积分卡密管理', url: '/admin-points-cards.html' }
    ];

    let frontendSuccess = 0;
    for (const page of pages) {
      try {
        const response = await webAxios.get(page.url);
        if (response.status === 200 && response.data.includes('<html')) {
          frontendSuccess++;
          logger.info(`✅ ${page.name} 页面正常`);
        }
      } catch (error) {
        logger.error(`❌ ${page.name} 页面无法访问`);
      }
    }

    if (frontendSuccess === pages.length) {
      results.frontend = true;
      logger.info(`✅ 前端页面验证通过 (${frontendSuccess}/${pages.length})`);
    } else {
      logger.warn(`⚠️ 部分前端页面无法访问 (${frontendSuccess}/${pages.length})`);
    }

    // 4. 验证后端API
    logger.info('🔌 验证后端API...');
    const apis = [
      { name: '获取分类列表', endpoint: '/categories' },
      { name: '获取内容列表', endpoint: '/content' },
      { name: '内容搜索', endpoint: '/content/search?keyword=示例' },
      { name: '生成微信二维码', endpoint: '/wechat/qr-login', method: 'POST' }
    ];

    let apiSuccess = 0;
    for (const api of apis) {
      try {
        let response;
        if (api.method === 'POST') {
          response = await apiAxios.post(api.endpoint, {});
        } else {
          response = await apiAxios.get(api.endpoint);
        }
        
        if (response.data.code === 200) {
          apiSuccess++;
          logger.info(`✅ ${api.name} API正常`);
        }
      } catch (error) {
        logger.error(`❌ ${api.name} API异常`);
      }
    }

    if (apiSuccess === apis.length) {
      results.backend = true;
      logger.info(`✅ 后端API验证通过 (${apiSuccess}/${apis.length})`);
    } else {
      logger.warn(`⚠️ 部分后端API异常 (${apiSuccess}/${apis.length})`);
    }

    // 5. 验证管理员功能
    logger.info('👨‍💼 验证管理员功能...');
    try {
      // 管理员登录
      const loginResponse = await apiAxios.post('/admin/login', {
        username: 'admin',
        password: 'admin123'
      });

      if (loginResponse.data.code === 200) {
        const token = loginResponse.data.data.token;
        logger.info('✅ 管理员登录成功');

        // 验证管理员权限
        const profileResponse = await apiAxios.get('/admin/profile', {
          headers: { 'Authorization': `Bearer ${token}` }
        });

        if (profileResponse.data.code === 200) {
          results.admin = true;
          logger.info('✅ 管理员权限验证通过');
          logger.info(`   管理员: ${profileResponse.data.data.nickname}`);
        }
      }
    } catch (error) {
      logger.error('❌ 管理员功能验证失败');
      logger.error(`   错误: ${error.response?.data?.message || error.message}`);
    }

    // 6. 总体评估
    logger.info('');
    logger.info('📊 部署验证结果:');
    logger.info('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    
    const checks = [
      { name: '服务器启动', status: results.server },
      { name: '数据库连接', status: results.database },
      { name: '前端页面', status: results.frontend },
      { name: '后端API', status: results.backend },
      { name: '管理员功能', status: results.admin }
    ];

    let passedChecks = 0;
    checks.forEach(check => {
      const status = check.status ? '✅ 通过' : '❌ 失败';
      logger.info(`   ${check.name}: ${status}`);
      if (check.status) passedChecks++;
    });

    const successRate = (passedChecks / checks.length * 100).toFixed(1);
    results.overall = passedChecks === checks.length;

    logger.info('');
    logger.info(`📈 验证通过率: ${successRate}% (${passedChecks}/${checks.length})`);
    
    if (results.overall) {
      logger.info('🎉 部署验证完全通过！系统可以正常使用');
      logger.info('');
      logger.info('🌐 访问地址:');
      logger.info('   管理后台: http://localhost:3000/admin-dashboard.html');
      logger.info('   微信登录: http://localhost:3000/wechat-qr-login.html');
      logger.info('   内容管理: http://localhost:3000/content-management.html');
      logger.info('');
      logger.info('🔑 演示账号: admin / admin123');
    } else {
      logger.warn('⚠️ 部分功能验证失败，请检查配置和日志');
      
      // 提供故障排除建议
      logger.info('');
      logger.info('🔧 故障排除建议:');
      if (!results.server) {
        logger.info('   - 检查服务器是否启动: node test-server.js');
        logger.info('   - 检查端口是否被占用: lsof -i :3000');
      }
      if (!results.database) {
        logger.info('   - 检查数据库连接: mysql -h ************* -P 3306 -u root -p');
        logger.info('   - 检查网络连接: ping *************');
      }
      if (!results.frontend) {
        logger.info('   - 检查静态文件目录: ls -la public/');
      }
      if (!results.backend) {
        logger.info('   - 检查API路由配置');
        logger.info('   - 查看服务器日志: tail -f logs/app.log');
      }
      if (!results.admin) {
        logger.info('   - 检查管理员账号是否存在');
        logger.info('   - 验证JWT配置');
      }
    }

    logger.info('');
    logger.info('📋 如需更多帮助，请查看 README.md 中的故障排除部分');

  } catch (error) {
    logger.error('❌ 验证过程中发生错误:', error.message);
    results.overall = false;
  }

  return results;
}

// 如果直接运行此文件
if (require.main === module) {
  verifyDeployment().then((results) => {
    if (results.overall) {
      logger.info('✅ 部署验证完成 - 系统正常');
      process.exit(0);
    } else {
      logger.error('❌ 部署验证失败 - 存在问题');
      process.exit(1);
    }
  }).catch((error) => {
    logger.error('部署验证异常:', error);
    process.exit(1);
  });
}

module.exports = {
  verifyDeployment
};
