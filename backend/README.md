# 知识付费平台

一个功能完整的知识付费平台，支持微信扫码登录、内容管理、积分系统、卡密管理等核心功能。

## 🚀 项目特性

- ✅ **微信扫码登录系统** - 支持微信开放平台扫码登录
- ✅ **内容管理系统** - 完整的内容CRUD、分类管理、搜索筛选
- ✅ **多种解锁机制** - 积分解锁、VIP解锁、卡密解锁、免费内容
- ✅ **管理后台系统** - 用户管理、内容管理、数据统计
- ✅ **积分配置系统** - 灵活的积分获取规则配置
- ✅ **卡密管理系统** - 批量生成、管理各种类型卡密
- ✅ **权限验证机制** - JWT认证、角色权限控制
- ✅ **响应式设计** - 支持桌面端和移动端访问

## 🚀 快速开始

### 一键部署（推荐）

#### Linux/macOS
```bash
# 下载并运行部署脚本
chmod +x deploy.sh
./deploy.sh
```

#### Windows
```cmd
# 双击运行或在命令行执行
deploy.bat
```

### 验证部署
```bash
# 验证系统是否正常运行
node verify-deployment.js
```

部署完成后，访问 http://localhost:3000/admin-dashboard.html 开始使用！

---

## 📋 系统要求

### 运行环境
- **Node.js**: >= 14.0.0 (推荐 16.x 或 18.x)
- **MySQL**: >= 5.7 或 8.0
- **操作系统**: Windows 10+, macOS 10.14+, Ubuntu 18.04+

### 硬件要求
- **内存**: 最低 2GB，推荐 4GB+
- **存储**: 最低 1GB 可用空间
- **网络**: 稳定的互联网连接

## 🛠️ 安装步骤

### 1. 克隆项目代码

```bash
git clone <repository-url>
cd zhis-platform
```

### 2. 安装依赖包

```bash
# 进入后端目录
cd backend

# 安装Node.js依赖
npm install
```

### 3. 数据库配置

#### 3.1 数据库连接信息
```
服务器地址: *************
端口: 3306
数据库名: zhis
用户名: root
密码: mysql_jZKFP62
```

#### 3.2 创建数据库表结构

```sql
-- 连接到MySQL数据库
mysql -h ************* -P 3306 -u root -p

-- 使用数据库
USE zhis;

-- 创建用户表
CREATE TABLE users (
  id INT PRIMARY KEY AUTO_INCREMENT,
  openid VARCHAR(100) UNIQUE NOT NULL,
  nickname VARCHAR(100),
  avatar VARCHAR(255),
  phone VARCHAR(20),
  points INT DEFAULT 0,
  vip_level INT DEFAULT 0,
  status TINYINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建分类表
CREATE TABLE categories (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL,
  slug VARCHAR(100) UNIQUE NOT NULL,
  description TEXT,
  sort_order INT DEFAULT 0,
  status TINYINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建内容表
CREATE TABLE contents (
  id INT PRIMARY KEY AUTO_INCREMENT,
  title VARCHAR(255) NOT NULL,
  summary TEXT,
  content LONGTEXT,
  content_type TINYINT DEFAULT 1,
  category_id INT,
  unlock_type TINYINT DEFAULT 1,
  unlock_price INT DEFAULT 0,
  view_count INT DEFAULT 0,
  like_count INT DEFAULT 0,
  unlock_count INT DEFAULT 0,
  is_recommend TINYINT DEFAULT 0,
  is_hot TINYINT DEFAULT 0,
  status TINYINT DEFAULT 1,
  created_by INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (category_id) REFERENCES categories(id),
  FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 创建内容解锁记录表
CREATE TABLE content_unlocks (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  content_id INT NOT NULL,
  unlock_type TINYINT NOT NULL,
  unlock_price INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (content_id) REFERENCES contents(id),
  UNIQUE KEY unique_user_content (user_id, content_id)
);

-- 创建积分配置表
CREATE TABLE point_configs (
  id INT PRIMARY KEY AUTO_INCREMENT,
  type VARCHAR(50) UNIQUE NOT NULL,
  points INT NOT NULL,
  description VARCHAR(255),
  is_enabled TINYINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建卡密表
CREATE TABLE card_codes (
  id INT PRIMARY KEY AUTO_INCREMENT,
  code VARCHAR(50) UNIQUE NOT NULL,
  type TINYINT NOT NULL,
  value INT DEFAULT 0,
  description VARCHAR(255),
  status TINYINT DEFAULT 1,
  expires_at TIMESTAMP NULL,
  created_by INT,
  used_by INT NULL,
  used_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (created_by) REFERENCES users(id),
  FOREIGN KEY (used_by) REFERENCES users(id)
);

-- 创建积分日志表
CREATE TABLE point_logs (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  points INT NOT NULL,
  type VARCHAR(50) NOT NULL,
  description VARCHAR(255),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);
```

### 4. 环境变量配置

创建 `.env` 文件：

```bash
# 数据库配置
DB_HOST=*************
DB_PORT=3306
DB_NAME=zhis
DB_USER=root
DB_PASSWORD=mysql_jZKFP62

# 服务器配置
PORT=3000
NODE_ENV=development

# JWT配置
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=7d

# 微信配置
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log
```

## 🚀 运行说明

### 开发环境启动

```bash
# 启动开发服务器
npm run dev

# 或者使用测试服务器（包含模拟数据）
node test-server.js
```

### 生产环境部署

```bash
# 安装PM2进程管理器
npm install -g pm2

# 启动生产服务器
pm2 start ecosystem.config.js

# 查看服务状态
pm2 status

# 查看日志
pm2 logs

# 重启服务
pm2 restart all

# 停止服务
pm2 stop all
```

### 测试服务器启动

```bash
# 启动测试服务器（推荐用于演示）
node test-server.js

# 服务器将在 http://localhost:3000 启动
```

## 🌐 功能验证

### 访问地址

| 功能模块 | 访问地址 | 说明 |
|---------|---------|------|
| 微信扫码登录 | http://localhost:3000/wechat-qr-login.html | 用户登录入口 |
| 内容管理演示 | http://localhost:3000/content-management.html | 内容浏览和管理 |
| 管理员登录 | http://localhost:3000/admin-login.html | 管理员登录入口 |
| 管理后台 | http://localhost:3000/admin-dashboard.html | 系统管理后台 |
| 积分卡密管理 | http://localhost:3000/admin-points-cards.html | 积分配置和卡密管理 |

### 演示账号

#### 管理员账号
- **用户名**: `admin`
- **密码**: `admin123`
- **权限**: 超级管理员，拥有所有权限

#### 测试用户
- 通过微信扫码登录自动创建
- 或使用模拟登录功能

### 测试脚本运行

```bash
# 运行完整系统测试
node test-complete-system.js

# 运行内容管理测试
node test-content-management.js

# 运行管理后台测试
node test-admin-system.js

# 运行积分卡密测试
node test-points-cards.js

# 运行解锁机制测试
node test-unlock-scenarios.js

# 运行前端功能测试
node frontend-validation-test.js

# 运行综合检测
node comprehensive-test-suite.js

# 运行最终演示
node final-demo-script.js
```

## 📊 API文档

### 用户认证相关

```bash
# 生成微信登录二维码
POST /api/v1/wechat/qr-login

# 检查登录状态
GET /api/v1/wechat/qr-login-status?ticket={ticket}

# 模拟扫码操作
POST /api/v1/wechat/simulate-scan
```

### 内容管理相关

```bash
# 获取分类列表
GET /api/v1/categories

# 获取内容列表
GET /api/v1/content

# 搜索内容
GET /api/v1/content/search?keyword={keyword}

# 获取内容详情
GET /api/v1/content/{id}

# 检查解锁状态
GET /api/v1/content/{id}/unlock-status
```

### 管理员相关

```bash
# 管理员登录
POST /api/v1/admin/login

# 获取管理员信息
GET /api/v1/admin/profile

# 获取统计数据
GET /api/v1/admin/stats

# 内容管理
POST /api/v1/admin/contents
PUT /api/v1/admin/contents/{id}
DELETE /api/v1/admin/contents/{id}

# 分类管理
POST /api/v1/admin/categories
DELETE /api/v1/admin/categories/{id}
```

### 积分卡密相关

```bash
# 获取积分配置
GET /api/v1/admin/point-configs

# 更新积分配置
PUT /api/v1/admin/point-configs/{type}

# 获取卡密统计
GET /api/v1/admin/card-codes/stats

# 批量生成卡密
POST /api/v1/admin/card-codes/batch-generate

# 获取卡密列表
GET /api/v1/admin/card-codes

# 删除卡密
DELETE /api/v1/admin/card-codes/{id}
```

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 数据库连接失败

**问题**: `Error: connect ECONNREFUSED *************:3306`

**解决方案**:
```bash
# 检查数据库服务是否运行
mysql -h ************* -P 3306 -u root -p

# 检查网络连接
ping *************

# 检查防火墙设置
# 确保3306端口开放

# 验证数据库配置
node test-config.js
```

#### 2. 端口占用问题

**问题**: `Error: listen EADDRINUSE :::3000`

**解决方案**:
```bash
# 查找占用端口的进程
lsof -i :3000
# 或者在Windows上
netstat -ano | findstr :3000

# 杀死占用进程
kill -9 <PID>

# 或者更改端口
export PORT=3001
node test-server.js
```

#### 3. 依赖包安装失败

**问题**: `npm install` 失败

**解决方案**:
```bash
# 清理npm缓存
npm cache clean --force

# 删除node_modules重新安装
rm -rf node_modules package-lock.json
npm install

# 使用淘宝镜像
npm install --registry https://registry.npm.taobao.org

# 或者使用yarn
npm install -g yarn
yarn install
```

#### 4. 权限验证失败

**问题**: `401 Unauthorized`

**解决方案**:
```bash
# 检查JWT令牌是否正确
# 确保请求头包含: Authorization: Bearer <token>

# 重新登录获取新令牌
curl -X POST http://localhost:3000/api/v1/admin/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'
```

#### 5. 前端页面无法访问

**问题**: `502 Bad Gateway` 或页面无法加载

**解决方案**:
```bash
# 检查服务器是否正常运行
curl http://localhost:3000/api/v1/categories

# 检查静态文件服务
ls -la public/

# 重启服务器
pm2 restart all
# 或者
node test-server.js
```

### 日志查看方法

#### 应用日志
```bash
# 查看实时日志
tail -f logs/app.log

# 查看错误日志
grep "ERROR" logs/app.log

# 查看最近100行日志
tail -n 100 logs/app.log
```

#### PM2日志
```bash
# 查看所有应用日志
pm2 logs

# 查看特定应用日志
pm2 logs zhis-platform

# 清空日志
pm2 flush
```

#### 数据库日志
```bash
# MySQL错误日志位置
# Linux: /var/log/mysql/error.log
# Windows: MySQL安装目录/data/hostname.err

# 查看MySQL慢查询日志
mysql -u root -p -e "SHOW VARIABLES LIKE 'slow_query_log%';"
```

### 调试建议

#### 1. 开启调试模式
```bash
# 设置环境变量
export NODE_ENV=development
export LOG_LEVEL=debug

# 启动调试模式
node --inspect test-server.js
```

#### 2. 使用调试工具
```bash
# 安装调试工具
npm install -g nodemon

# 使用nodemon自动重启
nodemon test-server.js

# 使用Chrome DevTools调试
# 打开 chrome://inspect
```

#### 3. 数据库调试
```sql
-- 查看数据库连接
SHOW PROCESSLIST;

-- 查看表结构
DESCRIBE users;
DESCRIBE contents;

-- 查看数据
SELECT * FROM users LIMIT 5;
SELECT * FROM contents LIMIT 5;

-- 检查索引
SHOW INDEX FROM contents;
```

## 📈 性能优化

### 数据库优化
```sql
-- 添加索引
CREATE INDEX idx_contents_category ON contents(category_id);
CREATE INDEX idx_contents_status ON contents(status);
CREATE INDEX idx_users_openid ON users(openid);

-- 查看查询执行计划
EXPLAIN SELECT * FROM contents WHERE category_id = 1;
```

### 应用优化
```bash
# 启用gzip压缩
# 在Nginx配置中添加:
gzip on;
gzip_types text/plain text/css application/json application/javascript;

# 使用Redis缓存
npm install redis
```

### 监控配置
```bash
# 安装监控工具
npm install -g pm2-logrotate
pm2 install pm2-logrotate

# 配置日志轮转
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 30
```

## 🔒 安全配置

### 生产环境安全设置

1. **更改默认密码**
```sql
-- 更改管理员密码
UPDATE admins SET password = '$2b$10$newhashedpassword' WHERE username = 'admin';
```

2. **配置HTTPS**
```bash
# 使用Let's Encrypt获取SSL证书
sudo certbot --nginx -d yourdomain.com
```

3. **配置防火墙**
```bash
# 只开放必要端口
sudo ufw allow 80
sudo ufw allow 443
sudo ufw allow 22
sudo ufw enable
```

4. **环境变量安全**
```bash
# 生产环境使用强密码
JWT_SECRET=$(openssl rand -base64 32)
DB_PASSWORD=your_strong_password_here
```

## 📦 项目结构

```
backend/
├── src/                    # 源代码目录
│   ├── controllers/        # 控制器
│   ├── models/            # 数据模型
│   ├── routes/            # 路由配置
│   ├── middleware/        # 中间件
│   ├── utils/             # 工具函数
│   └── config/            # 配置文件
├── public/                # 静态文件
│   ├── wechat-qr-login.html
│   ├── content-management.html
│   ├── admin-login.html
│   ├── admin-dashboard.html
│   └── admin-points-cards.html
├── logs/                  # 日志文件
├── test-*.js             # 测试脚本
├── database/             # 数据库相关
├── .env                  # 环境变量
├── package.json          # 项目配置
└── README.md            # 项目说明
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 技术支持

如果您在部署或使用过程中遇到问题，请：

1. 查看本文档的故障排除部分
2. 运行测试脚本验证系统状态
3. 检查日志文件获取错误信息
4. 提交 Issue 描述问题详情

## 🎯 版本信息

- **当前版本**: v1.0.0
- **Node.js**: >= 14.0.0
- **MySQL**: >= 5.7
- **最后更新**: 2025年7月6日

---

**🎉 恭喜！您已成功部署知识付费平台！**

访问 http://localhost:3000/admin-dashboard.html 开始使用管理后台。
