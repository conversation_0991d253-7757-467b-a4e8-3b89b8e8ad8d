const axios = require('axios');
const logger = require('./src/utils/logger');

const baseURL = 'http://localhost:3000/api/v1';

// 配置axios以绕过代理
const axiosInstance = axios.create({
  baseURL,
  proxy: false,
  timeout: 10000
});

// 模拟管理员令牌（实际需要通过管理员登录获取）
const mockAdminToken = 'mock_admin_token_for_testing';

async function testContentManagement() {
  try {
    logger.info('开始测试内容管理功能...');

    // 测试1: 获取分类列表
    logger.info('测试1: 获取分类列表');
    try {
      const categoriesResponse = await axiosInstance.get('/categories');
      if (categoriesResponse.data.code === 200) {
        logger.info(`✅ 获取分类列表成功，共 ${categoriesResponse.data.data.length} 个分类`);
        categoriesResponse.data.data.forEach(category => {
          logger.info(`  - ${category.name} (ID: ${category.id}, Slug: ${category.slug})`);
        });
      }
    } catch (error) {
      logger.error(`❌ 获取分类列表失败: ${error.response?.data?.message || error.message}`);
    }

    // 测试2: 获取内容列表
    logger.info('测试2: 获取内容列表');
    try {
      const contentsResponse = await axiosInstance.get('/content');
      if (contentsResponse.data.code === 200) {
        const contents = contentsResponse.data.data.contents || [];
        logger.info(`✅ 获取内容列表成功，共 ${contents.length} 个内容`);
        contents.slice(0, 3).forEach(content => {
          logger.info(`  - ${content.title} (ID: ${content.id}, 类型: ${content.contentTypeText})`);
        });
      }
    } catch (error) {
      logger.error(`❌ 获取内容列表失败: ${error.response?.data?.message || error.message}`);
    }

    // 测试3: 搜索内容
    logger.info('测试3: 搜索内容');
    try {
      const searchResponse = await axiosInstance.get('/content/search?keyword=示例');
      if (searchResponse.data.code === 200) {
        const results = searchResponse.data.data.contents || [];
        logger.info(`✅ 搜索功能正常，找到 ${results.length} 个结果`);
      }
    } catch (error) {
      logger.error(`❌ 搜索功能失败: ${error.response?.data?.message || error.message}`);
    }

    // 测试4: 创建测试内容（需要管理员权限）
    logger.info('测试4: 创建测试内容');
    const testContent = {
      title: '测试内容 - ' + Date.now(),
      summary: '这是一个测试内容的摘要',
      content: '这是测试内容的详细内容，用于验证内容管理功能是否正常工作。',
      contentType: 1, // 文章
      categoryId: 1,
      unlockType: 4, // 免费
      unlockPrice: 0,
      isRecommend: 1,
      isHot: 0
    };

    try {
      const createResponse = await axiosInstance.post('/admin/contents', testContent, {
        headers: {
          'Authorization': `Bearer ${mockAdminToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (createResponse.data.code === 200) {
        const newContent = createResponse.data.data;
        logger.info(`✅ 创建内容成功，ID: ${newContent.id}`);

        // 测试5: 获取内容详情
        logger.info('测试5: 获取内容详情');
        try {
          const detailResponse = await axiosInstance.get(`/content/${newContent.id}`);
          if (detailResponse.data.code === 200) {
            const detail = detailResponse.data.data;
            logger.info(`✅ 获取内容详情成功: ${detail.title}`);
            logger.info(`  - 浏览次数: ${detail.viewCount}`);
            logger.info(`  - 解锁状态: ${detail.isUnlocked ? '已解锁' : '未解锁'}`);
          }
        } catch (error) {
          logger.error(`❌ 获取内容详情失败: ${error.response?.data?.message || error.message}`);
        }

        // 测试6: 检查解锁状态
        logger.info('测试6: 检查解锁状态');
        try {
          const unlockStatusResponse = await axiosInstance.get(`/content/${newContent.id}/unlock-status`);
          if (unlockStatusResponse.data.code === 200) {
            const unlockStatus = unlockStatusResponse.data.data;
            logger.info(`✅ 检查解锁状态成功: ${unlockStatus.isUnlocked ? '已解锁' : '未解锁'}`);
          }
        } catch (error) {
          logger.error(`❌ 检查解锁状态失败: ${error.response?.data?.message || error.message}`);
        }

        // 测试7: 更新内容
        logger.info('测试7: 更新内容');
        try {
          const updateData = {
            title: testContent.title + ' (已更新)',
            isRecommend: 0
          };

          const updateResponse = await axiosInstance.put(`/admin/contents/${newContent.id}`, updateData, {
            headers: {
              'Authorization': `Bearer ${mockAdminToken}`,
              'Content-Type': 'application/json'
            }
          });

          if (updateResponse.data.code === 200) {
            logger.info(`✅ 更新内容成功`);
          }
        } catch (error) {
          logger.error(`❌ 更新内容失败: ${error.response?.data?.message || error.message}`);
        }

        // 测试8: 删除内容
        logger.info('测试8: 删除内容');
        try {
          const deleteResponse = await axiosInstance.delete(`/admin/contents/${newContent.id}`, {
            headers: {
              'Authorization': `Bearer ${mockAdminToken}`
            }
          });

          if (deleteResponse.data.code === 200) {
            logger.info(`✅ 删除内容成功`);
          }
        } catch (error) {
          logger.error(`❌ 删除内容失败: ${error.response?.data?.message || error.message}`);
        }

      }
    } catch (error) {
      logger.error(`❌ 创建内容失败: ${error.response?.data?.message || error.message}`);
      logger.info('💡 提示: 创建内容需要有效的管理员令牌和数据库连接');
    }

    // 测试9: 分类管理功能
    logger.info('测试9: 分类管理功能');
    const testCategory = {
      name: '测试分类 - ' + Date.now(),
      slug: 'test-category-' + Date.now(),
      description: '这是一个测试分类',
      sortOrder: 99
    };

    try {
      const createCategoryResponse = await axiosInstance.post('/admin/categories', testCategory, {
        headers: {
          'Authorization': `Bearer ${mockAdminToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (createCategoryResponse.data.code === 200) {
        const newCategory = createCategoryResponse.data.data;
        logger.info(`✅ 创建分类成功，ID: ${newCategory.id}`);

        // 删除测试分类
        try {
          await axiosInstance.delete(`/admin/categories/${newCategory.id}`, {
            headers: {
              'Authorization': `Bearer ${mockAdminToken}`
            }
          });
          logger.info(`✅ 删除测试分类成功`);
        } catch (error) {
          logger.error(`❌ 删除测试分类失败: ${error.response?.data?.message || error.message}`);
        }
      }
    } catch (error) {
      logger.error(`❌ 分类管理测试失败: ${error.response?.data?.message || error.message}`);
    }

    logger.info('🎉 内容管理功能测试完成！');
    logger.info('');
    logger.info('📱 要体验完整功能，请访问:');
    logger.info('1. 内容管理演示: http://localhost:3000/content-management.html');
    logger.info('2. 微信扫码登录: http://localhost:3000/wechat-qr-login.html');
    logger.info('');
    logger.info('⚠️  注意事项:');
    logger.info('- 管理员功能需要有效的管理员令牌');
    logger.info('- 用户功能需要用户登录令牌');
    logger.info('- 某些功能需要数据库连接正常');

  } catch (error) {
    logger.error('❌ 测试过程中发生错误:', error.message);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  testContentManagement().then(() => {
    logger.info('内容管理测试完成');
    process.exit(0);
  }).catch((error) => {
    logger.error('内容管理测试失败:', error);
    process.exit(1);
  });
}

module.exports = {
  testContentManagement
};
