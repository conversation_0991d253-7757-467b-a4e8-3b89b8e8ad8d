const Koa = require('koa');
const Router = require('koa-router');
const bodyParser = require('koa-bodyparser');
const cors = require('@koa/cors');
const serve = require('koa-static');
const path = require('path');
const logger = require('./src/utils/logger');

// 创建应用
const app = new Koa();
const router = new Router();

// 中间件
app.use(cors());
app.use(bodyParser());
app.use(serve(path.join(__dirname, 'public')));

// 错误处理
app.use(async (ctx, next) => {
  try {
    await next();
  } catch (error) {
    logger.error('请求处理失败', error);
    ctx.status = error.status || 500;
    ctx.body = {
      code: ctx.status,
      message: error.message || '服务器内部错误',
      timestamp: new Date().toISOString()
    };
  }
});

// 微信扫码登录相关路由
const qrLoginCache = new Map();

// 生成微信扫码登录二维码
router.post('/api/v1/wechat/qr-login', async (ctx) => {
  const QRCode = require('qrcode');
  
  try {
    // 生成唯一的登录票据
    const ticket = `qr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const timestamp = Date.now();
    
    // 模拟微信开放平台扫码登录URL（实际需要真实的appId）
    const appId = 'wx1234567890abcdef'; // 示例appId
    const redirectUri = 'http://localhost:3000/api/v1/wechat/qr-login-callback';
    const state = `${ticket}_${timestamp}`;
    
    const qrLoginUrl = `https://open.weixin.qq.com/connect/qrconnect?` +
      `appid=${appId}&` +
      `redirect_uri=${encodeURIComponent(redirectUri)}&` +
      `response_type=code&` +
      `scope=snsapi_login&` +
      `state=${state}#wechat_redirect`;

    // 生成二维码
    const qrCodeDataURL = await QRCode.toDataURL(qrLoginUrl, {
      width: 300,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    });

    // 存储登录状态
    qrLoginCache.set(ticket, {
      status: 'waiting',
      timestamp,
      ip: ctx.ip,
      userAgent: ctx.headers['user-agent']
    });

    // 设置过期时间（5分钟）
    setTimeout(() => {
      if (qrLoginCache.has(ticket)) {
        qrLoginCache.set(ticket, { ...qrLoginCache.get(ticket), status: 'expired' });
      }
    }, 5 * 60 * 1000);

    logger.info('生成微信扫码登录二维码', { ticket, ip: ctx.ip });

    ctx.body = {
      code: 200,
      message: '二维码生成成功',
      data: {
        ticket,
        qrCode: qrCodeDataURL,
        qrUrl: qrLoginUrl,
        expiresIn: 300
      }
    };
  } catch (error) {
    logger.error('生成微信扫码登录二维码失败', error);
    ctx.status = 500;
    ctx.body = {
      code: 500,
      message: '生成登录二维码失败'
    };
  }
});

// 检查扫码登录状态
router.get('/api/v1/wechat/qr-login-status', async (ctx) => {
  const { ticket } = ctx.query;

  if (!ticket) {
    ctx.status = 400;
    ctx.body = {
      code: 400,
      message: '登录票据不能为空'
    };
    return;
  }

  const loginInfo = qrLoginCache.get(ticket);
  
  if (!loginInfo) {
    ctx.status = 404;
    ctx.body = {
      code: 404,
      message: '登录票据不存在或已过期',
      data: { status: 'expired' }
    };
    return;
  }

  ctx.body = {
    code: 200,
    message: '获取状态成功',
    data: {
      status: loginInfo.status,
      user: loginInfo.user || null,
      token: loginInfo.token || null
    }
  };
});

// 微信扫码登录回调处理
router.get('/api/v1/wechat/qr-login-callback', async (ctx) => {
  const { code, state } = ctx.query;

  if (!code || !state) {
    ctx.body = `
      <html>
        <head><title>登录失败</title></head>
        <body>
          <div style="text-align: center; padding: 50px;">
            <h2>登录失败</h2>
            <p>授权参数不完整</p>
          </div>
        </body>
      </html>
    `;
    return;
  }

  try {
    // 解析state获取ticket
    const [ticket] = state.split('_');
    const loginInfo = qrLoginCache.get(ticket);

    if (!loginInfo || loginInfo.status === 'expired') {
      ctx.body = `
        <html>
          <head><title>登录失败</title></head>
          <body>
            <div style="text-align: center; padding: 50px;">
              <h2>登录失败</h2>
              <p>登录已过期，请重新扫码</p>
            </div>
          </body>
        </html>
      `;
      return;
    }

    // 模拟用户信息（实际需要调用微信API获取）
    const mockUser = {
      id: Math.floor(Math.random() * 10000),
      openid: `mock_openid_${Date.now()}`,
      nickname: '微信用户' + Math.floor(Math.random() * 1000),
      avatar: 'https://via.placeholder.com/100x100'
    };

    // 模拟JWT令牌
    const mockToken = `mock_token_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // 更新登录状态
    qrLoginCache.set(ticket, {
      ...loginInfo,
      status: 'confirmed',
      user: mockUser,
      token: mockToken
    });

    logger.info('微信扫码登录成功（模拟）', { 
      ticket,
      userId: mockUser.id,
      ip: loginInfo.ip 
    });

    // 返回成功页面
    ctx.body = `
      <html>
        <head><title>登录成功</title></head>
        <body>
          <div style="text-align: center; padding: 50px;">
            <h2>登录成功</h2>
            <p>您已成功登录，请返回原页面继续操作</p>
            <p style="color: #666; font-size: 14px;">用户: ${mockUser.nickname}</p>
            <script>
              // 通知父窗口登录成功
              if (window.opener) {
                window.opener.postMessage({
                  type: 'wechat_login_success',
                  ticket: '${ticket}',
                  token: '${mockToken}'
                }, '*');
                window.close();
              }
            </script>
          </div>
        </body>
      </html>
    `;
  } catch (error) {
    logger.error('微信扫码登录回调处理失败', error);
    
    ctx.body = `
      <html>
        <head><title>登录失败</title></head>
        <body>
          <div style="text-align: center; padding: 50px;">
            <h2>登录失败</h2>
            <p>登录过程中发生错误，请重试</p>
          </div>
        </body>
      </html>
    `;
  }
});

// 模拟扫码状态变化（用于演示）
router.post('/api/v1/wechat/simulate-scan', async (ctx) => {
  const { ticket, action } = ctx.request.body;
  
  if (!ticket) {
    ctx.status = 400;
    ctx.body = { code: 400, message: '票据不能为空' };
    return;
  }

  const loginInfo = qrLoginCache.get(ticket);
  if (!loginInfo) {
    ctx.status = 404;
    ctx.body = { code: 404, message: '票据不存在' };
    return;
  }

  if (action === 'scan') {
    qrLoginCache.set(ticket, { ...loginInfo, status: 'scanned' });
    ctx.body = { code: 200, message: '模拟扫码成功' };
  } else if (action === 'confirm') {
    const mockUser = {
      id: Math.floor(Math.random() * 10000),
      openid: `mock_openid_${Date.now()}`,
      nickname: '微信用户' + Math.floor(Math.random() * 1000),
      avatar: 'https://via.placeholder.com/100x100'
    };
    const mockToken = `mock_token_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    qrLoginCache.set(ticket, { 
      ...loginInfo, 
      status: 'confirmed',
      user: mockUser,
      token: mockToken
    });
    ctx.body = { code: 200, message: '模拟确认登录成功', data: { user: mockUser, token: mockToken } };
  } else {
    ctx.status = 400;
    ctx.body = { code: 400, message: '无效的操作' };
  }
});

app.use(router.routes());
app.use(router.allowedMethods());

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  logger.info(`🚀 测试服务器启动成功，端口: ${PORT}`);
  logger.info(`📱 微信扫码登录演示页面: http://localhost:${PORT}/wechat-qr-login.html`);
});

module.exports = app;
