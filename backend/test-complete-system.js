const axios = require('axios');
const logger = require('./src/utils/logger');

const baseURL = 'http://localhost:3000/api/v1';

// 配置axios以绕过代理
const axiosInstance = axios.create({
  baseURL,
  proxy: false,
  timeout: 10000
});

async function testCompleteSystem() {
  try {
    logger.info('🚀 开始知识付费平台完整系统测试...');
    logger.info('');

    // 测试1: 微信扫码登录功能
    logger.info('📱 测试1: 微信扫码登录功能');
    try {
      // 生成登录二维码
      const qrResponse = await axiosInstance.post('/wechat/qr-login');
      if (qrResponse.data.code === 200) {
        const ticket = qrResponse.data.data.ticket;
        logger.info(`✅ 生成微信登录二维码成功，票据: ${ticket}`);

        // 检查登录状态
        const statusResponse = await axiosInstance.get(`/wechat/qr-login-status?ticket=${ticket}`);
        if (statusResponse.data.code === 200) {
          logger.info(`✅ 登录状态检查正常: ${statusResponse.data.data.status}`);
        }

        // 模拟扫码确认
        const confirmResponse = await axiosInstance.post('/wechat/simulate-scan', {
          ticket,
          action: 'confirm'
        });
        if (confirmResponse.data.code === 200) {
          logger.info(`✅ 模拟扫码登录成功`);
        }
      }
    } catch (error) {
      logger.error(`❌ 微信扫码登录测试失败: ${error.response?.data?.message || error.message}`);
    }

    // 测试2: 内容管理系统
    logger.info('📚 测试2: 内容管理系统');
    try {
      // 获取分类列表
      const categoriesResponse = await axiosInstance.get('/categories');
      if (categoriesResponse.data.code === 200) {
        logger.info(`✅ 获取分类列表成功，共 ${categoriesResponse.data.data.length} 个分类`);
      }

      // 获取内容列表
      const contentsResponse = await axiosInstance.get('/content');
      if (contentsResponse.data.code === 200) {
        const contents = contentsResponse.data.data.contents || [];
        logger.info(`✅ 获取内容列表成功，共 ${contents.length} 个内容`);
      }

      // 搜索内容
      const searchResponse = await axiosInstance.get('/content/search?keyword=示例');
      if (searchResponse.data.code === 200) {
        const results = searchResponse.data.data.contents || [];
        logger.info(`✅ 内容搜索功能正常，找到 ${results.length} 个结果`);
      }

      // 获取内容详情
      const detailResponse = await axiosInstance.get('/content/1');
      if (detailResponse.data.code === 200) {
        logger.info(`✅ 获取内容详情成功: ${detailResponse.data.data.title}`);
      }

      // 检查解锁状态
      const unlockStatusResponse = await axiosInstance.get('/content/1/unlock-status');
      if (unlockStatusResponse.data.code === 200) {
        logger.info(`✅ 解锁状态检查正常: ${unlockStatusResponse.data.data.isUnlocked ? '已解锁' : '未解锁'}`);
      }
    } catch (error) {
      logger.error(`❌ 内容管理系统测试失败: ${error.response?.data?.message || error.message}`);
    }

    // 测试3: 管理后台系统
    logger.info('🔧 测试3: 管理后台系统');
    let adminToken = null;
    
    try {
      // 管理员登录
      const loginResponse = await axiosInstance.post('/admin/login', {
        username: 'admin',
        password: 'admin123'
      });

      if (loginResponse.data.code === 200) {
        adminToken = loginResponse.data.data.token;
        logger.info(`✅ 管理员登录成功`);

        // 获取管理员信息
        const profileResponse = await axiosInstance.get('/admin/profile', {
          headers: { 'Authorization': `Bearer ${adminToken}` }
        });
        if (profileResponse.data.code === 200) {
          logger.info(`✅ 获取管理员信息成功: ${profileResponse.data.data.nickname}`);
        }

        // 获取统计数据
        const statsResponse = await axiosInstance.get('/admin/stats', {
          headers: { 'Authorization': `Bearer ${adminToken}` }
        });
        if (statsResponse.data.code === 200) {
          const stats = statsResponse.data.data;
          logger.info(`✅ 获取统计数据成功 - 用户: ${stats.users.total}, 内容: ${stats.contents.total}`);
        }

        // 创建测试内容
        const createContentResponse = await axiosInstance.post('/admin/contents', {
          title: '系统测试内容 - ' + Date.now(),
          summary: '这是系统测试创建的内容',
          content: '测试内容详情...',
          contentType: 1,
          categoryId: 1,
          unlockType: 4,
          unlockPrice: 0
        }, {
          headers: {
            'Authorization': `Bearer ${adminToken}`,
            'Content-Type': 'application/json'
          }
        });

        if (createContentResponse.data.code === 200) {
          const contentId = createContentResponse.data.data.id;
          logger.info(`✅ 管理员创建内容成功，ID: ${contentId}`);

          // 删除测试内容
          await axiosInstance.delete(`/admin/contents/${contentId}`, {
            headers: { 'Authorization': `Bearer ${adminToken}` }
          });
          logger.info(`✅ 管理员删除内容成功`);
        }
      }
    } catch (error) {
      logger.error(`❌ 管理后台系统测试失败: ${error.response?.data?.message || error.message}`);
    }

    // 测试4: 积分配置和卡密管理
    logger.info('💰 测试4: 积分配置和卡密管理');
    if (adminToken) {
      try {
        // 获取积分配置
        const configsResponse = await axiosInstance.get('/admin/point-configs', {
          headers: { 'Authorization': `Bearer ${adminToken}` }
        });
        if (configsResponse.data.code === 200) {
          logger.info(`✅ 获取积分配置成功，共 ${configsResponse.data.data.length} 个配置`);
        }

        // 获取卡密统计
        const cardStatsResponse = await axiosInstance.get('/admin/card-codes/stats', {
          headers: { 'Authorization': `Bearer ${adminToken}` }
        });
        if (cardStatsResponse.data.code === 200) {
          const stats = cardStatsResponse.data.data;
          logger.info(`✅ 获取卡密统计成功 - 总数: ${stats.total}, 未使用: ${stats.byStatus.unused}`);
        }

        // 生成测试卡密
        const generateResponse = await axiosInstance.post('/admin/card-codes/batch-generate', {
          count: 3,
          type: 1,
          value: 100,
          description: '系统测试卡密'
        }, {
          headers: {
            'Authorization': `Bearer ${adminToken}`,
            'Content-Type': 'application/json'
          }
        });

        if (generateResponse.data.code === 200) {
          logger.info(`✅ 生成测试卡密成功，数量: ${generateResponse.data.data.count}`);
        }
      } catch (error) {
        logger.error(`❌ 积分配置和卡密管理测试失败: ${error.response?.data?.message || error.message}`);
      }
    }

    // 测试5: 权限验证
    logger.info('🔒 测试5: 权限验证');
    try {
      // 测试无效令牌
      try {
        await axiosInstance.get('/admin/profile', {
          headers: { 'Authorization': 'Bearer invalid_token' }
        });
      } catch (error) {
        if (error.response?.status === 401) {
          logger.info('✅ 无效令牌被正确拒绝');
        }
      }

      // 测试无令牌访问
      try {
        await axiosInstance.get('/admin/stats');
      } catch (error) {
        if (error.response?.status === 401) {
          logger.info('✅ 无令牌访问被正确拒绝');
        }
      }
    } catch (error) {
      logger.error(`❌ 权限验证测试失败: ${error.message}`);
    }

    logger.info('');
    logger.info('🎉 知识付费平台完整系统测试完成！');
    logger.info('');
    logger.info('📊 系统功能总结:');
    logger.info('✅ 微信扫码登录系统');
    logger.info('✅ 内容管理系统（CRUD、分类、搜索、解锁）');
    logger.info('✅ 管理后台系统（登录、用户管理、内容管理）');
    logger.info('✅ 积分配置和卡密管理');
    logger.info('✅ 权限验证和安全控制');
    logger.info('');
    logger.info('🌐 系统访问地址:');
    logger.info('1. 微信扫码登录: http://localhost:3000/wechat-qr-login.html');
    logger.info('2. 内容管理演示: http://localhost:3000/content-management.html');
    logger.info('3. 管理员登录: http://localhost:3000/admin-login.html');
    logger.info('4. 管理后台: http://localhost:3000/admin-dashboard.html');
    logger.info('5. 积分卡密管理: http://localhost:3000/admin-points-cards.html');
    logger.info('');
    logger.info('🔑 演示账号:');
    logger.info('管理员 - 用户名: admin, 密码: admin123');
    logger.info('');
    logger.info('💡 系统特性:');
    logger.info('- 响应式设计，支持移动端和桌面端');
    logger.info('- 完整的权限验证和安全机制');
    logger.info('- 多种内容类型支持（文章、网盘、视频、文档）');
    logger.info('- 灵活的解锁机制（积分、VIP、卡密、免费）');
    logger.info('- 完善的管理后台和数据统计');
    logger.info('- 可扩展的积分配置和卡密系统');

  } catch (error) {
    logger.error('❌ 完整系统测试过程中发生错误:', error.message);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  testCompleteSystem().then(() => {
    logger.info('知识付费平台完整系统测试完成');
    process.exit(0);
  }).catch((error) => {
    logger.error('知识付费平台完整系统测试失败:', error);
    process.exit(1);
  });
}

module.exports = {
  testCompleteSystem
};
