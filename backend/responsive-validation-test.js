const puppeteer = require('puppeteer');
const logger = require('./src/utils/logger');

class ResponsiveValidationTest {
  constructor() {
    this.browser = null;
    this.page = null;
    this.testResults = {
      responsive: [],
      validation: [],
      interaction: [],
      issues: []
    };
  }

  async runTests() {
    logger.info('🔍 开始响应式设计和表单验证测试...');
    
    try {
      // 启动浏览器
      this.browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      });
      
      this.page = await this.browser.newPage();
      
      // 测试响应式设计
      await this.testResponsiveDesign();
      
      // 测试表单验证
      await this.testFormValidation();
      
      // 测试用户交互
      await this.testUserInteraction();
      
      // 生成报告
      this.generateReport();
      
    } catch (error) {
      logger.error('❌ 响应式和验证测试失败:', error.message);
      this.testResults.issues.push({
        type: 'CRITICAL',
        description: `测试执行失败: ${error.message}`
      });
    } finally {
      if (this.browser) {
        await this.browser.close();
      }
    }
  }

  async testResponsiveDesign() {
    logger.info('📱 测试响应式设计...');
    
    const viewports = [
      { name: '手机竖屏', width: 375, height: 667 },
      { name: '手机横屏', width: 667, height: 375 },
      { name: '平板竖屏', width: 768, height: 1024 },
      { name: '平板横屏', width: 1024, height: 768 },
      { name: '桌面', width: 1920, height: 1080 }
    ];

    const pages = [
      { name: '微信扫码登录', url: 'http://localhost:3000/wechat-qr-login.html' },
      { name: '管理员登录', url: 'http://localhost:3000/admin-login.html' },
      { name: '管理后台', url: 'http://localhost:3000/admin-dashboard.html' }
    ];

    for (const page of pages) {
      for (const viewport of viewports) {
        try {
          await this.page.setViewport(viewport);
          await this.page.goto(page.url, { waitUntil: 'networkidle0', timeout: 10000 });
          
          // 检查页面是否正确加载
          const title = await this.page.title();
          
          // 检查是否有水平滚动条（响应式问题的常见指标）
          const hasHorizontalScroll = await this.page.evaluate(() => {
            return document.body.scrollWidth > window.innerWidth;
          });
          
          // 检查关键元素是否可见
          const elementsVisible = await this.page.evaluate(() => {
            const elements = document.querySelectorAll('button, input, .btn');
            return Array.from(elements).every(el => {
              const rect = el.getBoundingClientRect();
              return rect.width > 0 && rect.height > 0;
            });
          });
          
          this.testResults.responsive.push({
            page: page.name,
            viewport: viewport.name,
            dimensions: `${viewport.width}x${viewport.height}`,
            status: !hasHorizontalScroll && elementsVisible ? 'PASS' : 'FAIL',
            hasHorizontalScroll,
            elementsVisible,
            title
          });
          
          if (!hasHorizontalScroll && elementsVisible) {
            logger.info(`✅ ${page.name} - ${viewport.name} 响应式正常`);
          } else {
            logger.warn(`⚠️ ${page.name} - ${viewport.name} 响应式问题`);
            this.testResults.issues.push({
              type: 'RESPONSIVE',
              description: `${page.name} 在 ${viewport.name} 下存在响应式问题`
            });
          }
          
        } catch (error) {
          this.testResults.responsive.push({
            page: page.name,
            viewport: viewport.name,
            status: 'ERROR',
            error: error.message
          });
          logger.error(`❌ ${page.name} - ${viewport.name} 测试失败: ${error.message}`);
        }
      }
    }
  }

  async testFormValidation() {
    logger.info('📝 测试表单验证...');
    
    // 测试管理员登录表单
    try {
      await this.page.goto('http://localhost:3000/admin-login.html', { waitUntil: 'networkidle0' });
      
      // 测试空表单提交
      await this.page.click('#loginBtn');
      
      // 检查是否有验证提示
      const hasValidation = await this.page.evaluate(() => {
        const usernameInput = document.getElementById('username');
        const passwordInput = document.getElementById('password');
        return usernameInput.checkValidity() === false || passwordInput.checkValidity() === false;
      });
      
      this.testResults.validation.push({
        form: '管理员登录',
        test: '空表单验证',
        status: hasValidation ? 'PASS' : 'FAIL',
        details: hasValidation ? '空表单被正确拒绝' : '空表单验证失败'
      });
      
      // 测试错误密码
      await this.page.type('#username', 'admin');
      await this.page.type('#password', 'wrongpassword');
      await this.page.click('#loginBtn');
      
      // 等待响应
      await this.page.waitForTimeout(2000);
      
      // 检查错误提示
      const errorMessage = await this.page.evaluate(() => {
        const alertContainer = document.getElementById('alertContainer');
        return alertContainer ? alertContainer.textContent : '';
      });
      
      this.testResults.validation.push({
        form: '管理员登录',
        test: '错误密码验证',
        status: errorMessage.includes('错误') || errorMessage.includes('失败') ? 'PASS' : 'FAIL',
        details: errorMessage || '无错误提示'
      });
      
      logger.info('✅ 管理员登录表单验证测试完成');
      
    } catch (error) {
      this.testResults.validation.push({
        form: '管理员登录',
        test: '表单验证',
        status: 'ERROR',
        error: error.message
      });
      logger.error(`❌ 管理员登录表单验证测试失败: ${error.message}`);
    }
  }

  async testUserInteraction() {
    logger.info('🖱️ 测试用户交互...');
    
    try {
      // 测试微信扫码登录页面交互
      await this.page.goto('http://localhost:3000/wechat-qr-login.html', { waitUntil: 'networkidle0' });
      
      // 点击生成二维码按钮
      await this.page.click('#generateQR');
      
      // 等待二维码生成
      await this.page.waitForTimeout(2000);
      
      // 检查二维码是否生成
      const qrCodeExists = await this.page.evaluate(() => {
        const qrContainer = document.getElementById('qrcode');
        return qrContainer && qrContainer.children.length > 0;
      });
      
      this.testResults.interaction.push({
        page: '微信扫码登录',
        interaction: '生成二维码',
        status: qrCodeExists ? 'PASS' : 'FAIL',
        details: qrCodeExists ? '二维码生成成功' : '二维码生成失败'
      });
      
      // 测试状态检查
      if (qrCodeExists) {
        await this.page.click('#checkStatus');
        await this.page.waitForTimeout(1000);
        
        const statusText = await this.page.evaluate(() => {
          const statusEl = document.getElementById('loginStatus');
          return statusEl ? statusEl.textContent : '';
        });
        
        this.testResults.interaction.push({
          page: '微信扫码登录',
          interaction: '状态检查',
          status: statusText ? 'PASS' : 'FAIL',
          details: statusText || '无状态信息'
        });
      }
      
      logger.info('✅ 微信扫码登录交互测试完成');
      
    } catch (error) {
      this.testResults.interaction.push({
        page: '微信扫码登录',
        interaction: '用户交互',
        status: 'ERROR',
        error: error.message
      });
      logger.error(`❌ 用户交互测试失败: ${error.message}`);
    }
  }

  generateReport() {
    logger.info('');
    logger.info('📋 ===== 响应式设计和表单验证测试报告 =====');
    logger.info('');
    
    // 响应式测试结果
    const responsiveTests = this.testResults.responsive;
    const responsivePass = responsiveTests.filter(t => t.status === 'PASS').length;
    const responsiveTotal = responsiveTests.length;
    
    logger.info('📱 响应式设计测试结果:');
    logger.info(`   总测试数: ${responsiveTotal}`);
    logger.info(`   通过测试: ${responsivePass}`);
    logger.info(`   通过率: ${((responsivePass / responsiveTotal) * 100).toFixed(2)}%`);
    
    // 按页面分组显示结果
    const pageGroups = {};
    responsiveTests.forEach(test => {
      if (!pageGroups[test.page]) {
        pageGroups[test.page] = [];
      }
      pageGroups[test.page].push(test);
    });
    
    Object.keys(pageGroups).forEach(pageName => {
      const pageTests = pageGroups[pageName];
      const pagePass = pageTests.filter(t => t.status === 'PASS').length;
      logger.info(`   ${pageName}: ${pagePass}/${pageTests.length} 通过`);
    });
    
    logger.info('');
    
    // 表单验证测试结果
    const validationTests = this.testResults.validation;
    const validationPass = validationTests.filter(t => t.status === 'PASS').length;
    
    logger.info('📝 表单验证测试结果:');
    logger.info(`   总测试数: ${validationTests.length}`);
    logger.info(`   通过测试: ${validationPass}`);
    
    validationTests.forEach(test => {
      const status = test.status === 'PASS' ? '✅' : '❌';
      logger.info(`   ${status} ${test.form} - ${test.test}: ${test.details}`);
    });
    
    logger.info('');
    
    // 用户交互测试结果
    const interactionTests = this.testResults.interaction;
    const interactionPass = interactionTests.filter(t => t.status === 'PASS').length;
    
    logger.info('🖱️ 用户交互测试结果:');
    logger.info(`   总测试数: ${interactionTests.length}`);
    logger.info(`   通过测试: ${interactionPass}`);
    
    interactionTests.forEach(test => {
      const status = test.status === 'PASS' ? '✅' : '❌';
      logger.info(`   ${status} ${test.page} - ${test.interaction}: ${test.details}`);
    });
    
    logger.info('');
    
    // 问题总结
    if (this.testResults.issues.length > 0) {
      logger.info('⚠️ 发现的问题:');
      this.testResults.issues.forEach((issue, index) => {
        logger.info(`   ${index + 1}. [${issue.type}] ${issue.description}`);
      });
      logger.info('');
    }
    
    // 总体评估
    const totalTests = responsiveTotal + validationTests.length + interactionTests.length;
    const totalPass = responsivePass + validationPass + interactionPass;
    const overallPassRate = ((totalPass / totalTests) * 100).toFixed(2);
    
    logger.info('🏆 总体评估:');
    logger.info(`   总测试数: ${totalTests}`);
    logger.info(`   通过测试: ${totalPass}`);
    logger.info(`   总通过率: ${overallPassRate}%`);
    logger.info(`   问题数量: ${this.testResults.issues.length}`);
    
    const overallStatus = overallPassRate >= 95 ? 'EXCELLENT' : 
                         overallPassRate >= 85 ? 'GOOD' : 
                         overallPassRate >= 70 ? 'FAIR' : 'POOR';
    
    logger.info(`   总体状态: ${overallStatus}`);
    logger.info('');
    logger.info('===== 测试报告结束 =====');
  }
}

// 运行测试
async function runResponsiveValidationTests() {
  const tester = new ResponsiveValidationTest();
  await tester.runTests();
}

// 如果直接运行此文件
if (require.main === module) {
  runResponsiveValidationTests().then(() => {
    logger.info('响应式设计和表单验证测试完成');
    process.exit(0);
  }).catch((error) => {
    logger.error('响应式设计和表单验证测试失败:', error);
    process.exit(1);
  });
}

module.exports = {
  ResponsiveValidationTest,
  runResponsiveValidationTests
};
