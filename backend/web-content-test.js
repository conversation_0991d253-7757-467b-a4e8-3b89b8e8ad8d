const axios = require('axios');
const logger = require('./src/utils/logger');
const cheerio = require('cheerio');

const baseURL = 'http://localhost:3000';

// 配置axios以绕过代理
const webAxios = axios.create({
  baseURL,
  proxy: false,
  timeout: 15000
});

class WebContentTest {
  constructor() {
    this.testResults = {
      pageContent: [],
      uiElements: [],
      interactions: [],
      responsiveness: [],
      accessibility: [],
      performance: [],
      issues: []
    };
  }

  async runWebContentTests() {
    logger.info('🌐 开始网页内容端全面测试...');
    logger.info('');

    try {
      // 1. 页面内容测试
      await this.testPageContent();
      
      // 2. UI元素测试
      await this.testUIElements();
      
      // 3. 交互功能测试
      await this.testInteractions();
      
      // 4. 响应式布局测试
      await this.testResponsiveLayout();
      
      // 5. 可访问性测试
      await this.testAccessibility();
      
      // 6. 性能测试
      await this.testPerformance();
      
      // 7. 生成测试报告
      this.generateReport();
      
    } catch (error) {
      logger.error('❌ 网页内容端测试失败:', error.message);
      this.testResults.issues.push({
        type: 'CRITICAL',
        description: `测试执行失败: ${error.message}`
      });
    }
  }

  async testPageContent() {
    logger.info('📄 测试页面内容...');
    
    const pages = [
      { 
        name: '微信扫码登录页面', 
        url: '/wechat-qr-login.html',
        expectedElements: ['#generateQR', '#qrcode', '#loginStatus', '.login-container']
      },
      { 
        name: '内容管理演示页面', 
        url: '/content-management.html',
        expectedElements: ['#contentList', '#categoryFilter', '#searchInput', '.content-item']
      },
      { 
        name: '管理员登录页面', 
        url: '/admin-login.html',
        expectedElements: ['#loginForm', '#username', '#password', '#loginBtn']
      },
      { 
        name: '管理后台页面', 
        url: '/admin-dashboard.html',
        expectedElements: ['.header', '.sidebar', '.content-area', '.stats-grid']
      },
      { 
        name: '积分卡密管理页面', 
        url: '/admin-points-cards.html',
        expectedElements: ['.tabs', '#pointsTab', '#cardsTab', '#generateCardForm']
      }
    ];

    for (const page of pages) {
      try {
        const response = await webAxios.get(page.url);
        const $ = cheerio.load(response.data);
        
        // 检查页面基本结构
        const hasTitle = $('title').length > 0;
        const hasMetaViewport = $('meta[name="viewport"]').length > 0;
        const hasCharset = $('meta[charset]').length > 0;
        
        // 检查预期元素
        const foundElements = [];
        const missingElements = [];
        
        page.expectedElements.forEach(selector => {
          if ($(selector).length > 0) {
            foundElements.push(selector);
          } else {
            missingElements.push(selector);
          }
        });
        
        // 检查页面内容
        const hasHeadings = $('h1, h2, h3').length > 0;
        const hasButtons = $('button, .btn').length > 0;
        const hasInputs = $('input, textarea, select').length > 0;
        const hasImages = $('img').length > 0;
        
        // 检查CSS样式
        const hasInlineCSS = $('style').length > 0;
        const hasExternalCSS = $('link[rel="stylesheet"]').length > 0;
        
        // 检查JavaScript
        const hasInlineJS = $('script').filter((i, el) => $(el).html().trim().length > 0).length > 0;
        const hasExternalJS = $('script[src]').length > 0;
        
        const contentScore = [
          hasTitle, hasMetaViewport, hasCharset, hasHeadings, 
          hasButtons, foundElements.length > 0
        ].filter(Boolean).length;
        
        this.testResults.pageContent.push({
          page: page.name,
          url: page.url,
          contentScore: `${contentScore}/6`,
          hasTitle,
          hasMetaViewport,
          hasCharset,
          hasHeadings,
          hasButtons,
          hasInputs,
          hasImages,
          hasInlineCSS,
          hasExternalCSS,
          hasInlineJS,
          hasExternalJS,
          foundElements: foundElements.length,
          missingElements: missingElements.length,
          totalElements: page.expectedElements.length,
          status: contentScore >= 5 && missingElements.length === 0 ? 'PASS' : 'FAIL'
        });
        
        if (contentScore >= 5 && missingElements.length === 0) {
          logger.info(`✅ ${page.name} 内容完整 (${contentScore}/6, 元素: ${foundElements.length}/${page.expectedElements.length})`);
        } else {
          logger.warn(`⚠️ ${page.name} 内容不完整 (${contentScore}/6, 缺失元素: ${missingElements.length})`);
          if (missingElements.length > 0) {
            logger.warn(`   缺失元素: ${missingElements.join(', ')}`);
          }
          this.testResults.issues.push({
            type: 'CONTENT',
            description: `${page.name} 页面内容不完整，缺失 ${missingElements.length} 个关键元素`
          });
        }
        
      } catch (error) {
        this.testResults.pageContent.push({
          page: page.name,
          url: page.url,
          status: 'ERROR',
          error: error.message
        });
        logger.error(`❌ ${page.name} 内容测试失败: ${error.message}`);
      }
    }
  }

  async testUIElements() {
    logger.info('🎨 测试UI元素...');
    
    const pages = [
      { name: '微信扫码登录', url: '/wechat-qr-login.html' },
      { name: '管理员登录', url: '/admin-login.html' },
      { name: '管理后台', url: '/admin-dashboard.html' }
    ];

    for (const page of pages) {
      try {
        const response = await webAxios.get(page.url);
        const $ = cheerio.load(response.data);
        
        // 检查按钮样式
        const buttons = $('button, .btn');
        const hasStyledButtons = buttons.length > 0;
        
        // 检查表单元素
        const inputs = $('input, textarea, select');
        const hasStyledInputs = inputs.length > 0;
        
        // 检查布局容器
        const hasContainers = $('.container, .wrapper, .main').length > 0;
        const hasHeaders = $('.header, header').length > 0;
        const hasFooters = $('.footer, footer').length > 0;
        
        // 检查导航元素
        const hasNavigation = $('.nav, .menu, .sidebar').length > 0;
        
        // 检查卡片/面板
        const hasCards = $('.card, .panel, .box').length > 0;
        
        // 检查图标
        const hasIcons = $('.icon, .fa, [class*="icon"]').length > 0;
        
        // 检查颜色主题
        const cssContent = $('style').html() || '';
        const hasColorScheme = cssContent.includes('color:') || cssContent.includes('background');
        
        // 检查响应式类
        const hasResponsiveClasses = cssContent.includes('@media') || 
                                   cssContent.includes('flex') || 
                                   cssContent.includes('grid');
        
        const uiScore = [
          hasStyledButtons, hasStyledInputs, hasContainers, hasHeaders,
          hasNavigation, hasColorScheme, hasResponsiveClasses
        ].filter(Boolean).length;
        
        this.testResults.uiElements.push({
          page: page.name,
          uiScore: `${uiScore}/7`,
          hasStyledButtons,
          hasStyledInputs,
          hasContainers,
          hasHeaders,
          hasFooters,
          hasNavigation,
          hasCards,
          hasIcons,
          hasColorScheme,
          hasResponsiveClasses,
          buttonCount: buttons.length,
          inputCount: inputs.length,
          status: uiScore >= 5 ? 'PASS' : 'FAIL'
        });
        
        if (uiScore >= 5) {
          logger.info(`✅ ${page.name} UI元素完善 (${uiScore}/7)`);
        } else {
          logger.warn(`⚠️ ${page.name} UI元素需要改进 (${uiScore}/7)`);
          this.testResults.issues.push({
            type: 'UI',
            description: `${page.name} UI元素不够完善`
          });
        }
        
      } catch (error) {
        this.testResults.uiElements.push({
          page: page.name,
          status: 'ERROR',
          error: error.message
        });
        logger.error(`❌ ${page.name} UI元素测试失败: ${error.message}`);
      }
    }
  }

  async testInteractions() {
    logger.info('🖱️ 测试交互功能...');
    
    // 测试微信扫码登录交互
    try {
      const response = await webAxios.get('/wechat-qr-login.html');
      const $ = cheerio.load(response.data);
      
      // 检查JavaScript事件处理
      const jsContent = $('script').html() || '';
      const hasClickHandlers = jsContent.includes('click') || jsContent.includes('addEventListener');
      const hasFormHandlers = jsContent.includes('submit') || jsContent.includes('onsubmit');
      const hasAsyncFunctions = jsContent.includes('async') && jsContent.includes('await');
      const hasFetchAPI = jsContent.includes('fetch(');
      const hasErrorHandling = jsContent.includes('try') && jsContent.includes('catch');
      
      // 检查DOM操作
      const hasDOMManipulation = jsContent.includes('getElementById') || 
                               jsContent.includes('querySelector') ||
                               jsContent.includes('innerHTML') ||
                               jsContent.includes('textContent');
      
      // 检查状态管理
      const hasStateManagement = jsContent.includes('localStorage') || 
                                jsContent.includes('sessionStorage') ||
                                jsContent.includes('state');
      
      const interactionScore = [
        hasClickHandlers, hasFormHandlers, hasAsyncFunctions, 
        hasFetchAPI, hasErrorHandling, hasDOMManipulation
      ].filter(Boolean).length;
      
      this.testResults.interactions.push({
        page: '微信扫码登录',
        interactionScore: `${interactionScore}/6`,
        hasClickHandlers,
        hasFormHandlers,
        hasAsyncFunctions,
        hasFetchAPI,
        hasErrorHandling,
        hasDOMManipulation,
        hasStateManagement,
        status: interactionScore >= 4 ? 'PASS' : 'FAIL'
      });
      
      if (interactionScore >= 4) {
        logger.info(`✅ 微信扫码登录 交互功能完善 (${interactionScore}/6)`);
      } else {
        logger.warn(`⚠️ 微信扫码登录 交互功能需要改进 (${interactionScore}/6)`);
      }
      
    } catch (error) {
      this.testResults.interactions.push({
        page: '微信扫码登录',
        status: 'ERROR',
        error: error.message
      });
      logger.error(`❌ 交互功能测试失败: ${error.message}`);
    }
  }

  async testResponsiveLayout() {
    logger.info('📱 测试响应式布局...');
    
    const pages = [
      { name: '微信扫码登录', url: '/wechat-qr-login.html' },
      { name: '管理员登录', url: '/admin-login.html' },
      { name: '管理后台', url: '/admin-dashboard.html' }
    ];

    for (const page of pages) {
      try {
        const response = await webAxios.get(page.url);
        const $ = cheerio.load(response.data);
        
        const cssContent = $('style').html() || '';
        
        // 检查媒体查询
        const hasMediaQueries = cssContent.includes('@media');
        const hasMobileBreakpoint = cssContent.includes('768px') || cssContent.includes('480px');
        const hasTabletBreakpoint = cssContent.includes('1024px') || cssContent.includes('768px');
        
        // 检查弹性布局
        const hasFlexbox = cssContent.includes('display: flex') || cssContent.includes('display:flex');
        const hasGrid = cssContent.includes('display: grid') || cssContent.includes('display:grid');
        
        // 检查响应式单位
        const hasResponsiveUnits = cssContent.includes('vw') || 
                                 cssContent.includes('vh') || 
                                 cssContent.includes('%') ||
                                 cssContent.includes('em') ||
                                 cssContent.includes('rem');
        
        // 检查最大宽度
        const hasMaxWidth = cssContent.includes('max-width');
        const hasMinWidth = cssContent.includes('min-width');
        
        // 检查移动端优化
        const hasTouchFriendly = cssContent.includes('touch') || 
                                cssContent.includes('pointer') ||
                                cssContent.includes('44px') || // 最小触摸目标
                                cssContent.includes('48px');
        
        const responsiveScore = [
          hasMediaQueries, hasMobileBreakpoint, hasFlexbox, 
          hasResponsiveUnits, hasMaxWidth
        ].filter(Boolean).length;
        
        this.testResults.responsiveness.push({
          page: page.name,
          responsiveScore: `${responsiveScore}/5`,
          hasMediaQueries,
          hasMobileBreakpoint,
          hasTabletBreakpoint,
          hasFlexbox,
          hasGrid,
          hasResponsiveUnits,
          hasMaxWidth,
          hasMinWidth,
          hasTouchFriendly,
          status: responsiveScore >= 3 ? 'PASS' : 'FAIL'
        });
        
        if (responsiveScore >= 3) {
          logger.info(`✅ ${page.name} 响应式布局良好 (${responsiveScore}/5)`);
        } else {
          logger.warn(`⚠️ ${page.name} 响应式布局需要改进 (${responsiveScore}/5)`);
          this.testResults.issues.push({
            type: 'RESPONSIVE',
            description: `${page.name} 响应式布局不足`
          });
        }
        
      } catch (error) {
        this.testResults.responsiveness.push({
          page: page.name,
          status: 'ERROR',
          error: error.message
        });
        logger.error(`❌ ${page.name} 响应式测试失败: ${error.message}`);
      }
    }
  }

  async testAccessibility() {
    logger.info('♿ 测试可访问性...');
    
    const pages = [
      { name: '微信扫码登录', url: '/wechat-qr-login.html' },
      { name: '管理员登录', url: '/admin-login.html' }
    ];

    for (const page of pages) {
      try {
        const response = await webAxios.get(page.url);
        const $ = cheerio.load(response.data);
        
        // 检查语义化标签
        const hasSemanticTags = $('main, section, article, nav, aside, header, footer').length > 0;
        
        // 检查标题结构
        const hasH1 = $('h1').length > 0;
        const hasHeadingHierarchy = $('h1, h2, h3, h4, h5, h6').length > 1;
        
        // 检查表单标签
        const inputs = $('input, textarea, select');
        const labels = $('label');
        const hasFormLabels = labels.length > 0 && inputs.length > 0;
        
        // 检查图片alt属性
        const images = $('img');
        const imagesWithAlt = $('img[alt]');
        const hasImageAlt = images.length === 0 || imagesWithAlt.length === images.length;
        
        // 检查ARIA属性
        const hasAriaLabels = $('[aria-label], [aria-labelledby], [aria-describedby]').length > 0;
        const hasAriaRoles = $('[role]').length > 0;
        
        // 检查焦点样式
        const cssContent = $('style').html() || '';
        const hasFocusStyles = cssContent.includes(':focus');
        
        // 检查键盘导航
        const hasTabIndex = $('[tabindex]').length > 0;
        
        // 检查颜色对比（简单检查）
        const hasColorContrast = cssContent.includes('#333') || 
                                cssContent.includes('#000') ||
                                cssContent.includes('black');
        
        const accessibilityScore = [
          hasSemanticTags, hasH1, hasFormLabels, hasImageAlt, hasFocusStyles
        ].filter(Boolean).length;
        
        this.testResults.accessibility.push({
          page: page.name,
          accessibilityScore: `${accessibilityScore}/5`,
          hasSemanticTags,
          hasH1,
          hasHeadingHierarchy,
          hasFormLabels,
          hasImageAlt,
          hasAriaLabels,
          hasAriaRoles,
          hasFocusStyles,
          hasTabIndex,
          hasColorContrast,
          imageCount: images.length,
          imagesWithAltCount: imagesWithAlt.length,
          status: accessibilityScore >= 3 ? 'PASS' : 'FAIL'
        });
        
        if (accessibilityScore >= 3) {
          logger.info(`✅ ${page.name} 可访问性良好 (${accessibilityScore}/5)`);
        } else {
          logger.warn(`⚠️ ${page.name} 可访问性需要改进 (${accessibilityScore}/5)`);
          this.testResults.issues.push({
            type: 'ACCESSIBILITY',
            description: `${page.name} 可访问性不足`
          });
        }
        
      } catch (error) {
        this.testResults.accessibility.push({
          page: page.name,
          status: 'ERROR',
          error: error.message
        });
        logger.error(`❌ ${page.name} 可访问性测试失败: ${error.message}`);
      }
    }
  }

  async testPerformance() {
    logger.info('⚡ 测试性能表现...');
    
    const pages = [
      { name: '微信扫码登录', url: '/wechat-qr-login.html' },
      { name: '管理后台', url: '/admin-dashboard.html' }
    ];

    for (const page of pages) {
      try {
        const startTime = Date.now();
        const response = await webAxios.get(page.url);
        const loadTime = Date.now() - startTime;
        
        const contentSize = response.data.length;
        const $ = cheerio.load(response.data);
        
        // 检查资源优化
        const cssCount = $('style, link[rel="stylesheet"]').length;
        const jsCount = $('script').length;
        const imageCount = $('img').length;
        
        // 检查压缩
        const isCompressed = response.headers['content-encoding'] === 'gzip';
        
        // 检查缓存
        const hasCacheHeaders = !!response.headers['cache-control'];
        
        // 检查异步加载
        const cssContent = $('style').html() || '';
        const jsContent = $('script').html() || '';
        const hasAsyncScripts = $('script[async], script[defer]').length > 0;
        
        // 性能评分
        const performanceScore = [
          loadTime < 1000, // 加载时间小于1秒
          contentSize < 50000, // 内容大小小于50KB
          cssCount <= 5, // CSS文件数量合理
          jsCount <= 10 // JS文件数量合理
        ].filter(Boolean).length;
        
        this.testResults.performance.push({
          page: page.name,
          loadTime: `${loadTime}ms`,
          contentSize: `${(contentSize / 1024).toFixed(2)}KB`,
          performanceScore: `${performanceScore}/4`,
          cssCount,
          jsCount,
          imageCount,
          isCompressed,
          hasCacheHeaders,
          hasAsyncScripts,
          status: performanceScore >= 3 && loadTime < 2000 ? 'PASS' : 'FAIL'
        });
        
        if (performanceScore >= 3 && loadTime < 2000) {
          logger.info(`✅ ${page.name} 性能表现良好 (${loadTime}ms, ${(contentSize / 1024).toFixed(2)}KB)`);
        } else {
          logger.warn(`⚠️ ${page.name} 性能需要优化 (${loadTime}ms, ${(contentSize / 1024).toFixed(2)}KB)`);
          this.testResults.issues.push({
            type: 'PERFORMANCE',
            description: `${page.name} 性能表现需要优化`
          });
        }
        
      } catch (error) {
        this.testResults.performance.push({
          page: page.name,
          status: 'ERROR',
          error: error.message
        });
        logger.error(`❌ ${page.name} 性能测试失败: ${error.message}`);
      }
    }
  }
