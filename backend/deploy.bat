@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 知识付费平台Windows快速部署脚本

echo 🚀 知识付费平台快速部署脚本 (Windows)
echo ==================================
echo.

:: 颜色定义（Windows CMD限制，使用简单输出）
set "INFO=[INFO]"
set "WARN=[WARN]"
set "ERROR=[ERROR]"
set "STEP=[STEP]"

:: 检查Node.js安装
echo %STEP% 检查Node.js安装状态...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo %ERROR% Node.js未安装，请先安装Node.js 14.x或更高版本
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
    echo %INFO% Node.js已安装: !NODE_VERSION!
)

:: 检查npm
echo %STEP% 检查npm...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo %ERROR% npm未找到，请检查Node.js安装
    pause
    exit /b 1
) else (
    echo %INFO% npm可用
)

:: 检查MySQL客户端
echo %STEP% 检查MySQL客户端...
mysql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo %WARN% MySQL客户端未安装，将跳过数据库连接测试
    echo 请确保数据库已正确配置
    set SKIP_DB_TEST=1
) else (
    echo %INFO% MySQL客户端已安装
    set SKIP_DB_TEST=0
)

:: 测试数据库连接
if !SKIP_DB_TEST! equ 0 (
    echo %STEP% 测试数据库连接...
    mysql -h ************* -P 3306 -u root -pmysql_jZKFP62 -e "SELECT 1;" >nul 2>&1
    if !errorlevel! neq 0 (
        echo %ERROR% 数据库连接失败，请检查网络连接和数据库配置
        echo 数据库地址: *************:3306
        echo 用户名: root
        pause
        exit /b 1
    ) else (
        echo %INFO% 数据库连接成功
    )
    
    :: 初始化数据库
    echo %STEP% 初始化数据库...
    if exist "database\setup.sql" (
        mysql -h ************* -P 3306 -u root -pmysql_jZKFP62 < database\setup.sql
        if !errorlevel! equ 0 (
            echo %INFO% 数据库初始化完成
        ) else (
            echo %ERROR% 数据库初始化失败
            pause
            exit /b 1
        )
    ) else (
        echo %ERROR% 数据库初始化脚本不存在: database\setup.sql
        pause
        exit /b 1
    )
)

:: 安装项目依赖
echo %STEP% 安装项目依赖...
if exist "package.json" (
    npm install
    if !errorlevel! equ 0 (
        echo %INFO% 依赖安装完成
    ) else (
        echo %ERROR% 依赖安装失败
        pause
        exit /b 1
    )
) else (
    echo %ERROR% package.json文件不存在
    pause
    exit /b 1
)

:: 创建环境变量文件
echo %STEP% 创建环境变量文件...
if not exist ".env" (
    (
        echo # 数据库配置
        echo DB_HOST=*************
        echo DB_PORT=3306
        echo DB_NAME=zhis
        echo DB_USER=root
        echo DB_PASSWORD=mysql_jZKFP62
        echo.
        echo # 服务器配置
        echo PORT=3000
        echo NODE_ENV=production
        echo.
        echo # JWT配置
        echo JWT_SECRET=your_jwt_secret_key_here_change_in_production
        echo JWT_EXPIRES_IN=7d
        echo.
        echo # 微信配置
        echo WECHAT_APP_ID=your_wechat_app_id
        echo WECHAT_APP_SECRET=your_wechat_app_secret
        echo.
        echo # 日志配置
        echo LOG_LEVEL=info
        echo LOG_FILE=logs/app.log
    ) > .env
    echo %INFO% 环境变量文件创建完成
) else (
    echo %INFO% 环境变量文件已存在
)

:: 创建日志目录
echo %STEP% 创建日志目录...
if not exist "logs" (
    mkdir logs
    echo %INFO% 日志目录创建完成
) else (
    echo %INFO% 日志目录已存在
)

:: 检查PM2
echo %STEP% 检查PM2进程管理器...
pm2 --version >nul 2>&1
if %errorlevel% neq 0 (
    echo %STEP% 安装PM2...
    npm install -g pm2
    if !errorlevel! equ 0 (
        echo %INFO% PM2安装完成
    ) else (
        echo %WARN% PM2安装失败，将使用普通方式启动
        set USE_PM2=0
    )
) else (
    echo %INFO% PM2已安装
    set USE_PM2=1
)

:: 创建PM2配置文件
if !USE_PM2! equ 1 (
    echo %STEP% 创建PM2配置文件...
    (
        echo module.exports = {
        echo   apps: [{
        echo     name: 'zhis-platform',
        echo     script: 'test-server.js',
        echo     instances: 1,
        echo     autorestart: true,
        echo     watch: false,
        echo     max_memory_restart: '1G',
        echo     env: {
        echo       NODE_ENV: 'production',
        echo       PORT: 3000
        echo     },
        echo     error_file: './logs/err.log',
        echo     out_file: './logs/out.log',
        echo     log_file: './logs/combined.log',
        echo     time: true
        echo   }]
        echo };
    ) > ecosystem.config.js
    echo %INFO% PM2配置文件创建完成
)

:: 运行测试
echo %STEP% 运行系统测试...
start /b node test-server.js
timeout /t 5 /nobreak >nul

node test-complete-system.js
if !errorlevel! equ 0 (
    echo %INFO% 系统测试通过
) else (
    echo %ERROR% 系统测试失败
    taskkill /f /im node.exe >nul 2>&1
    pause
    exit /b 1
)

:: 停止测试服务器
taskkill /f /im node.exe >nul 2>&1

:: 启动服务
echo %STEP% 启动服务...
if !USE_PM2! equ 1 (
    pm2 start ecosystem.config.js
    pm2 save
    echo %INFO% 服务启动完成（PM2管理）
) else (
    echo %INFO% 启动开发服务器...
    start "知识付费平台" node test-server.js
    echo %INFO% 服务启动完成（开发模式）
)

:: 显示部署结果
echo.
echo 🎉 部署完成！
echo ==================================
echo.
echo 📊 系统信息:
echo   - 服务地址: http://localhost:3000
echo   - 数据库: *************:3306/zhis
echo   - 日志目录: .\logs\
echo.
echo 🌐 访问地址:
echo   - 微信扫码登录: http://localhost:3000/wechat-qr-login.html
echo   - 内容管理演示: http://localhost:3000/content-management.html
echo   - 管理员登录: http://localhost:3000/admin-login.html
echo   - 管理后台: http://localhost:3000/admin-dashboard.html
echo   - 积分卡密管理: http://localhost:3000/admin-points-cards.html
echo.
echo 🔑 演示账号:
echo   - 用户名: admin
echo   - 密码: admin123
echo.
echo 🔧 管理命令:
if !USE_PM2! equ 1 (
    echo   - 查看状态: pm2 status
    echo   - 查看日志: pm2 logs
    echo   - 重启服务: pm2 restart zhis-platform
    echo   - 停止服务: pm2 stop zhis-platform
) else (
    echo   - 查看日志: type logs\app.log
    echo   - 停止服务: 关闭服务器窗口
)
echo.
echo 📖 更多信息请查看 README.md
echo.
echo 按任意键打开管理后台...
pause >nul

:: 打开浏览器
start http://localhost:3000/admin-dashboard.html

echo.
echo 部署完成！服务正在运行中...
pause
