const jwt = require('jsonwebtoken');
const config = require('../config/config');
const { User, Admin, AdminRole } = require('../models');
const logger = require('../utils/logger');

// JWT认证中间件
async function authMiddleware(ctx, next) {
  try {
    const token = getTokenFromRequest(ctx);
    
    if (!token) {
      ctx.status = 401;
      ctx.body = {
        code: 401,
        message: '未提供认证令牌',
        timestamp: new Date().toISOString()
      };
      return;
    }

    // 验证JWT令牌
    const decoded = jwt.verify(token, config.jwt.secret);

    // 查找用户
    const user = await User.findByPk(decoded.userId);
    if (!user) {
      ctx.status = 401;
      ctx.body = {
        code: 401,
        message: '用户不存在',
        timestamp: new Date().toISOString()
      };
      return;
    }

    if (user.status !== 1) {
      ctx.status = 401;
      ctx.body = {
        code: 401,
        message: '用户已被禁用',
        timestamp: new Date().toISOString()
      };
      return;
    }

    // 将用户信息添加到上下文
    ctx.state.user = user;
    ctx.state.token = token;

    await next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      ctx.status = 401;
      ctx.body = {
        code: 401,
        message: '无效的认证令牌',
        timestamp: new Date().toISOString()
      };
    } else if (error.name === 'TokenExpiredError') {
      ctx.status = 401;
      ctx.body = {
        code: 401,
        message: '认证令牌已过期',
        timestamp: new Date().toISOString()
      };
    } else {
      logger.error('认证中间件错误', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '认证服务异常',
        timestamp: new Date().toISOString()
      };
    }
  }
}

// 可选认证中间件（不强制要求登录）
async function optionalAuthMiddleware(ctx, next) {
  try {
    const token = getTokenFromRequest(ctx);
    
    if (token) {
      const decoded = jwt.verify(token, config.jwt.secret);
      const user = await User.findByPk(decoded.userId);

      if (user && user.status === 1) {
        ctx.state.user = user;
        ctx.state.token = token;
      }
    }
  } catch (error) {
    // 可选认证失败时不阻止请求继续
    logger.warn('可选认证失败', error.message);
  }
  
  await next();
}

// 管理员认证中间件
async function adminAuthMiddleware(ctx, next) {
  try {
    const token = getTokenFromRequest(ctx);
    
    if (!token) {
      ctx.status = 401;
      ctx.body = {
        code: 401,
        message: '未提供认证令牌',
        timestamp: new Date().toISOString()
      };
      return;
    }

    const decoded = jwt.verify(token, config.jwt.secret);

    // 查找管理员
    const admin = await Admin.findByPk(decoded.adminId, {
      include: [{
        model: AdminRole,
        as: 'role'
      }]
    });
    if (!admin) {
      ctx.status = 401;
      ctx.body = {
        code: 401,
        message: '管理员不存在',
        timestamp: new Date().toISOString()
      };
      return;
    }

    if (admin.status !== 1) {
      ctx.status = 401;
      ctx.body = {
        code: 401,
        message: '管理员已被禁用',
        timestamp: new Date().toISOString()
      };
      return;
    }

    // 获取角色权限
    const role = admin.role;
    if (!role) {
      ctx.status = 403;
      ctx.body = {
        code: 403,
        message: '角色不存在',
        timestamp: new Date().toISOString()
      };
      return;
    }

    ctx.state.admin = admin;
    ctx.state.role = role;
    ctx.state.token = token;

    await next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      ctx.status = 401;
      ctx.body = {
        code: 401,
        message: '无效的认证令牌',
        timestamp: new Date().toISOString()
      };
    } else if (error.name === 'TokenExpiredError') {
      ctx.status = 401;
      ctx.body = {
        code: 401,
        message: '认证令牌已过期',
        timestamp: new Date().toISOString()
      };
    } else {
      logger.error('管理员认证中间件错误', error);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        message: '认证服务异常',
        timestamp: new Date().toISOString()
      };
    }
  }
}

// 权限检查中间件
function permissionMiddleware(permission) {
  return async (ctx, next) => {
    const { role } = ctx.state;
    
    if (!role) {
      ctx.status = 403;
      ctx.body = {
        code: 403,
        message: '无权限访问',
        timestamp: new Date().toISOString()
      };
      return;
    }

    const permissions = JSON.parse(role.permissions || '[]');
    
    // 超级管理员拥有所有权限
    if (permissions.includes('*')) {
      await next();
      return;
    }

    // 检查具体权限
    const hasPermission = permissions.some(p => {
      if (p === permission) return true;
      if (p.endsWith('.*') && permission.startsWith(p.slice(0, -2))) return true;
      return false;
    });

    if (!hasPermission) {
      ctx.status = 403;
      ctx.body = {
        code: 403,
        message: '权限不足',
        timestamp: new Date().toISOString()
      };
      return;
    }

    await next();
  };
}

// 从请求中获取令牌
function getTokenFromRequest(ctx) {
  // 从Authorization头获取
  const authHeader = ctx.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.slice(7);
  }

  // 从查询参数获取
  if (ctx.query.token) {
    return ctx.query.token;
  }

  // 从Cookie获取
  if (ctx.cookies.get('token')) {
    return ctx.cookies.get('token');
  }

  return null;
}

// 生成JWT令牌
function generateToken(payload, expiresIn = config.jwt.expiresIn) {
  return jwt.sign(payload, config.jwt.secret, { expiresIn });
}

// 生成刷新令牌
function generateRefreshToken(payload) {
  return jwt.sign(payload, config.jwt.secret, { 
    expiresIn: config.jwt.refreshExpiresIn 
  });
}

// 验证令牌
function verifyToken(token) {
  return jwt.verify(token, config.jwt.secret);
}

module.exports = {
  authMiddleware,
  optionalAuthMiddleware,
  adminAuthMiddleware,
  permissionMiddleware,
  generateToken,
  generateRefreshToken,
  verifyToken,
  getTokenFromRequest
};
