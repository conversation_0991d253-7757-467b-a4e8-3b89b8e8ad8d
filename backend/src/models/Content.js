const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Content = sequelize.define('Content', {
    id: {
      type: DataTypes.BIGINT,
      primaryKey: true,
      autoIncrement: true,
      comment: '内容ID'
    },
    title: {
      type: DataTypes.STRING(200),
      allowNull: false,
      comment: '标题'
    },
    summary: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '摘要'
    },
    content: {
      type: DataTypes.TEXT('long'),
      allowNull: true,
      comment: '内容'
    },
    contentType: {
      type: DataTypes.TINYINT,
      allowNull: true,
      defaultValue: 1,
      comment: '内容类型：1文章 2网盘 3视频 4文档',
      field: 'content_type'
    },
    coverImage: {
      type: DataTypes.STRING(500),
      allowNull: true,
      comment: '封面图',
      field: 'cover_image'
    },
    categoryId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '分类ID',
      field: 'category_id'
    },
    unlockType: {
      type: DataTypes.TINYINT,
      allowNull: true,
      defaultValue: 1,
      comment: '解锁方式：1积分 2VIP 3卡密 4免费',
      field: 'unlock_type'
    },
    unlockPrice: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0,
      comment: '解锁价格（积分）',
      field: 'unlock_price'
    },
    viewCount: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0,
      comment: '浏览次数',
      field: 'view_count'
    },
    likeCount: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0,
      comment: '点赞次数',
      field: 'like_count'
    },
    unlockCount: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0,
      comment: '解锁次数',
      field: 'unlock_count'
    },
    status: {
      type: DataTypes.TINYINT,
      allowNull: true,
      defaultValue: 1,
      comment: '状态：1正常 2下架 3审核中'
    },
    isRecommend: {
      type: DataTypes.TINYINT,
      allowNull: true,
      defaultValue: 0,
      comment: '是否推荐',
      field: 'is_recommend'
    },
    isHot: {
      type: DataTypes.TINYINT,
      allowNull: true,
      defaultValue: 0,
      comment: '是否热门',
      field: 'is_hot'
    },
    createdBy: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '创建人',
      field: 'created_by'
    }
  }, {
    tableName: 'contents',
    comment: '内容表',
    indexes: [
      {
        fields: ['category_id']
      },
      {
        fields: ['unlock_type']
      },
      {
        fields: ['status']
      },
      {
        fields: ['is_recommend']
      },
      {
        fields: ['is_hot']
      },
      {
        fields: ['created_at']
      },
      {
        fields: ['view_count']
      },
      {
        fields: ['like_count']
      },
      {
        fields: ['unlock_count']
      },
      {
        fields: ['created_by']
      }
    ]
  });

  // 实例方法
  Content.prototype.toJSON = function() {
    const values = { ...this.get() };

    // 转换内容类型为可读格式
    const contentTypeMap = {
      1: 'article',
      2: 'netdisk',
      3: 'video',
      4: 'document'
    };
    values.contentTypeText = contentTypeMap[values.contentType] || 'article';

    // 转换解锁类型为可读格式
    const unlockTypeMap = {
      1: 'points',
      2: 'vip',
      3: 'card',
      4: 'free'
    };
    values.unlockTypeText = unlockTypeMap[values.unlockType] || 'points';

    return values;
  };

  // 检查用户是否可以访问内容
  Content.prototype.canAccess = function(user) {
    // 免费内容直接可访问
    if (this.unlockType === 4) {
      return true;
    }
    
    // 未登录用户不能访问付费内容
    if (!user) {
      return false;
    }
    
    // VIP内容检查VIP状态
    if (this.unlockType === 2) {
      return user.isVip();
    }
    
    // 积分内容检查积分余额
    if (this.unlockType === 1) {
      return user.points >= this.unlockPrice;
    }
    
    return false;
  };

  // 增加浏览次数
  Content.prototype.incrementViewCount = async function() {
    await this.increment('viewCount');
  };

  // 增加点赞次数
  Content.prototype.incrementLikeCount = async function() {
    await this.increment('likeCount');
  };

  // 减少点赞次数
  Content.prototype.decrementLikeCount = async function() {
    await this.decrement('likeCount');
  };

  // 增加解锁次数
  Content.prototype.incrementUnlockCount = async function() {
    await this.increment('unlockCount');
  };

  // 计算热度分数
  Content.prototype.getHotScore = function() {
    const now = new Date();
    const createTime = new Date(this.createdAt);
    const hoursSinceCreated = (now - createTime) / (1000 * 60 * 60);

    // 热度计算公式：(点赞数 * 2 + 解锁数 * 3 + 浏览数 * 1) / (时间衰减因子)
    const score = (
      this.likeCount * 2 +
      this.unlockCount * 3 +
      this.viewCount * 1
    ) / Math.pow(hoursSinceCreated + 2, 1.5);

    return Math.round(score * 100) / 100;
  };

  // 类方法
  // 获取热门内容
  Content.getHotContents = function(limit = 10) {
    return this.findAll({
      where: { status: 1 },
      order: [
        ['viewCount', 'DESC'],
        ['likeCount', 'DESC'],
        ['unlockCount', 'DESC']
      ],
      limit
    });
  };

  // 获取推荐内容
  Content.getRecommendContents = function(limit = 10) {
    return this.findAll({
      where: {
        status: 1,
        isRecommend: 1
      },
      order: [
        ['createdAt', 'DESC']
      ],
      limit
    });
  };

  // 获取最新内容
  Content.getLatestContents = function(limit = 10) {
    return this.findAll({
      where: { status: 1 },
      order: [['createdAt', 'DESC']],
      limit
    });
  };

  // 根据分类获取内容
  Content.getByCategory = function(categoryId, page = 1, pageSize = 20) {
    const offset = (page - 1) * pageSize;
    return this.findAndCountAll({
      where: {
        categoryId,
        status: 1
      },
      order: [
        ['isRecommend', 'DESC'],
        ['createdAt', 'DESC']
      ],
      limit: pageSize,
      offset
    });
  };

  // 搜索内容
  Content.search = function(keyword, page = 1, pageSize = 20) {
    const offset = (page - 1) * pageSize;
    const { Op } = require('sequelize');

    return this.findAndCountAll({
      where: {
        status: 1,
        [Op.or]: [
          { title: { [Op.like]: `%${keyword}%` } },
          { summary: { [Op.like]: `%${keyword}%` } }
        ]
      },
      order: [
        ['isRecommend', 'DESC'],
        ['viewCount', 'DESC'],
        ['createdAt', 'DESC']
      ],
      limit: pageSize,
      offset
    });
  };

  return Content;
};
