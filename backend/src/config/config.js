require('dotenv').config();

const config = {
  // 服务器配置
  server: {
    port: process.env.PORT || 3000,
    host: process.env.HOST || '0.0.0.0',
    env: process.env.NODE_ENV || 'development'
  },

  // 数据库配置
  database: {
    type: 'mysql', // 使用MySQL数据库
    host: process.env.DB_HOST || '*************',
    port: process.env.DB_PORT || 3306,
    username: process.env.DB_USERNAME || 'root',
    password: process.env.DB_PASSWORD || 'mysql_jZKFP62',
    database: process.env.DB_DATABASE || 'zhis',
    dialect: 'mysql',
    timezone: '+08:00',
    pool: {
      max: 10,
      min: 0,
      acquire: 30000,
      idle: 10000
    },
    logging: process.env.NODE_ENV === 'development' ? console.log : false
  },

  // Redis配置
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: process.env.REDIS_PORT || 6379,
    password: process.env.REDIS_PASSWORD || '',
    db: process.env.REDIS_DB || 0,
    keyPrefix: 'zhis:'
  },

  // JWT配置
  jwt: {
    secret: process.env.JWT_SECRET || 'zhis-jwt-secret-key',
    expiresIn: process.env.JWT_EXPIRES_IN || '7d',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '30d'
  },

  // 微信配置
  wechat: {
    appId: process.env.WECHAT_APP_ID || '',
    appSecret: process.env.WECHAT_APP_SECRET || '',
    mchId: process.env.WECHAT_MCH_ID || '',
    apiKey: process.env.WECHAT_API_KEY || '',
    notifyUrl: process.env.WECHAT_NOTIFY_URL || ''
  },

  // 文件上传配置
  upload: {
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    uploadPath: process.env.UPLOAD_PATH || './uploads',
    baseUrl: process.env.UPLOAD_BASE_URL || 'http://localhost:3000/uploads'
  },

  // OSS配置（可选）
  oss: {
    accessKeyId: process.env.OSS_ACCESS_KEY_ID || '',
    accessKeySecret: process.env.OSS_ACCESS_KEY_SECRET || '',
    bucket: process.env.OSS_BUCKET || '',
    region: process.env.OSS_REGION || '',
    endpoint: process.env.OSS_ENDPOINT || ''
  },

  // 跨域配置
  cors: {
    origin: process.env.CORS_ORIGIN || '*'
  },

  // 限流配置
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 100, // 最大请求次数
    message: '请求过于频繁，请稍后再试'
  },

  // 积分系统默认配置
  points: {
    // 每日签到
    dailySign: {
      base: 5,
      continuous7: 10,
      continuous30: 20
    },
    // 观看广告
    watchAd: {
      points: 3,
      maxPerDay: 5
    },
    // 邀请奖励
    invite: {
      register: 20,
      unlockRatio: 0.2
    },
    // 分享奖励
    share: {
      points: 2,
      maxPerDay: 10
    },
    // 其他奖励
    other: {
      completeProfile: 10,
      firstUnlock: 5,
      contentCreation: 100
    },
    // 限制
    limits: {
      expireDays: 365,
      dailyGainLimit: 200
    }
  },

  // 日志配置
  log: {
    level: process.env.LOG_LEVEL || 'info',
    file: process.env.LOG_FILE || './logs/app.log',
    maxSize: '20m',
    maxFiles: '14d'
  }
};

module.exports = config;
