const { Content, User, ContentUnlock, PointLog, CardCode } = require('../models');
const { BusinessError, ValidationError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const config = require('../config/config');

class UnlockController {
  // 解锁内容
  async unlockContent(ctx) {
    try {
      const { id } = ctx.params;
      const { unlockMethod, cardCode } = ctx.request.body;
      const user = ctx.state.user;

      if (!user) {
        throw new BusinessError('请先登录', 401);
      }

      // 获取内容信息
      const content = await Content.findByPk(id);
      if (!content) {
        throw new BusinessError('内容不存在', 404);
      }

      if (content.status !== 1) {
        throw new BusinessError('内容已下架', 403);
      }

      // 检查是否已经解锁
      const existingUnlock = await ContentUnlock.findOne({
        where: {
          userId: user.id,
          contentId: content.id
        }
      });

      if (existingUnlock) {
        ctx.body = {
          code: 200,
          message: '内容已解锁',
          data: {
            isUnlocked: true,
            unlockRecord: existingUnlock
          }
        };
        return;
      }

      // 如果是免费内容，直接解锁
      if (content.unlockType === 4) {
        const unlockRecord = await ContentUnlock.create({
          userId: user.id,
          contentId: content.id,
          unlockMethod: 'free',
          unlockPrice: 0
        });

        await content.incrementUnlockCount();

        ctx.body = {
          code: 200,
          message: '免费内容解锁成功',
          data: {
            isUnlocked: true,
            unlockRecord
          }
        };
        return;
      }

      // 根据解锁方式处理
      switch (content.unlockType) {
        case 1: // 积分解锁
          await this.unlockWithPoints(user, content);
          break;
        case 2: // VIP解锁
          await this.unlockWithVip(user, content);
          break;
        case 3: // 卡密解锁
          if (!cardCode) {
            throw new ValidationError('请输入卡密');
          }
          await this.unlockWithCard(user, content, cardCode);
          break;
        default:
          throw new BusinessError('不支持的解锁方式');
      }

      // 创建解锁记录
      const unlockRecord = await ContentUnlock.create({
        userId: user.id,
        contentId: content.id,
        unlockMethod: this.getUnlockMethodText(content.unlockType),
        unlockPrice: content.unlockPrice || 0
      });

      // 增加解锁次数
      await content.incrementUnlockCount();

      logger.business('解锁内容', user.id, {
        contentId: content.id,
        contentTitle: content.title,
        unlockMethod: unlockRecord.unlockMethod,
        unlockPrice: unlockRecord.unlockPrice
      });

      ctx.body = {
        code: 200,
        message: '解锁成功',
        data: {
          isUnlocked: true,
          unlockRecord
        }
      };
    } catch (error) {
      logger.error('解锁内容失败', error);
      if (error instanceof BusinessError || error instanceof ValidationError) {
        throw error;
      }
      throw new BusinessError('解锁失败');
    }
  }

  // 积分解锁
  async unlockWithPoints(user, content) {
    const unlockPrice = content.unlockPrice || 0;

    if (unlockPrice <= 0) {
      throw new BusinessError('积分价格配置错误');
    }

    if (user.points < unlockPrice) {
      throw new BusinessError('积分不足，无法解锁');
    }

    // 扣除积分
    await user.deductPoints(unlockPrice, `解锁内容：${content.title}`);
  }

  // VIP解锁
  async unlockWithVip(user, content) {
    if (!user.isVip()) {
      throw new BusinessError('需要VIP权限才能解锁此内容');
    }

    const remainingDays = user.getVipRemainingDays();
    if (remainingDays <= 0) {
      throw new BusinessError('VIP已过期，请续费后再试');
    }
  }

  // 卡密解锁
  async unlockWithCard(user, content, cardCode) {
    // 查找卡密
    const card = await CardCode.findOne({
      where: {
        code: cardCode,
        status: 1, // 未使用
        type: 2 // 内容解锁卡
      }
    });

    if (!card) {
      throw new BusinessError('卡密不存在或已使用');
    }

    if (card.expiresAt && new Date() > card.expiresAt) {
      throw new BusinessError('卡密已过期');
    }

    // 标记卡密为已使用
    await card.update({
      status: 2, // 已使用
      usedBy: user.id,
      usedAt: new Date()
    });

    logger.business('使用卡密解锁内容', user.id, {
      cardCode,
      contentId: content.id,
      contentTitle: content.title
    });
  }

  // 获取解锁方式文本
  getUnlockMethodText(unlockType) {
    const methodMap = {
      1: 'points',
      2: 'vip',
      3: 'card',
      4: 'free'
    };
    return methodMap[unlockType] || 'unknown';
  }

  // 检查内容解锁状态
  async checkUnlockStatus(ctx) {
    try {
      const { id } = ctx.params;
      const user = ctx.state.user;

      if (!user) {
        ctx.body = {
          code: 200,
          message: '获取成功',
          data: {
            isUnlocked: false,
            unlockRecord: null
          }
        };
        return;
      }

      const content = await Content.findByPk(id);
      if (!content) {
        throw new BusinessError('内容不存在', 404);
      }

      // 如果是免费内容，直接返回已解锁
      if (content.unlockType === 4) {
        ctx.body = {
          code: 200,
          message: '获取成功',
          data: {
            isUnlocked: true,
            unlockRecord: null
          }
        };
        return;
      }

      const unlockRecord = await ContentUnlock.findOne({
        where: {
          userId: user.id,
          contentId: content.id
        }
      });

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: {
          isUnlocked: !!unlockRecord,
          unlockRecord
        }
      };
    } catch (error) {
      logger.error('检查解锁状态失败', error);
      if (error instanceof BusinessError) {
        throw error;
      }
      throw new BusinessError('检查解锁状态失败');
    }
  }

  // 获取用户解锁记录
  async getUserUnlocks(ctx) {
    try {
      const { page = 1, pageSize = 20 } = ctx.query;
      const user = ctx.state.user;
      const offset = (page - 1) * pageSize;

      const { count, rows } = await ContentUnlock.findAndCountAll({
        where: { userId: user.id },
        include: [
          {
            model: Content,
            as: 'content',
            attributes: ['id', 'title', 'summary', 'coverImage', 'contentType']
          }
        ],
        order: [['createdAt', 'DESC']],
        limit: parseInt(pageSize),
        offset: parseInt(offset)
      });

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: {
          unlocks: rows,
          pagination: {
            page: parseInt(page),
            pageSize: parseInt(pageSize),
            total: count,
            totalPages: Math.ceil(count / pageSize)
          }
        }
      };
    } catch (error) {
      logger.error('获取用户解锁记录失败', error);
      throw new BusinessError('获取解锁记录失败');
    }
  }
}

module.exports = new UnlockController();
