const { Content, ContentUnlock, ContentLike, Favorite, Comment, CommentLike, ShareLog, User, Category, Tag } = require('../models');
const logger = require('../utils/logger');
const { BusinessError, ValidationError, NotFoundError } = require('../middleware/errorHandler');

class ContentController {
  // 获取内容列表
  async getList(ctx) {
    const { 
      page = 1, 
      pageSize = 20, 
      categoryId, 
      unlockType, 
      sort = 'latest' 
    } = ctx.query;

    const offset = (parseInt(page) - 1) * parseInt(pageSize);
    const where = { status: 1 };

    // 分类筛选
    if (categoryId) {
      where.categoryId = categoryId;
    }

    // 解锁方式筛选
    if (unlockType) {
      where.unlockType = unlockType;
    }

    // 排序方式
    let order = [['createdAt', 'DESC']];
    switch (sort) {
      case 'hot':
        order = [['viewCount', 'DESC'], ['likeCount', 'DESC']];
        break;
      case 'popular':
        order = [['unlockCount', 'DESC'], ['viewCount', 'DESC']];
        break;
      case 'recommend':
        order = [['isRecommend', 'DESC'], ['sortOrder', 'DESC'], ['createdAt', 'DESC']];
        break;
      case 'price_low':
        order = [['unlockPrice', 'ASC']];
        break;
      case 'price_high':
        order = [['unlockPrice', 'DESC']];
        break;
    }

    const result = await Content.findAndCountAll({
      where,
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name', 'slug']
        },
        {
          model: Tag,
          as: 'tags',
          attributes: ['id', 'name', 'color'],
          through: { attributes: [] }
        }
      ],
      order,
      limit: parseInt(pageSize),
      offset,
      attributes: {
        exclude: ['content'] // 列表不返回详细内容
      }
    });

    ctx.body = {
      code: 200,
      message: '获取成功',
      data: {
        list: result.rows,
        total: result.count,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      }
    };
  }

  // 搜索内容
  async search(ctx) {
    const { keyword, page = 1, pageSize = 20 } = ctx.query;

    if (!keyword) {
      throw new ValidationError('搜索关键词不能为空');
    }

    const result = await Content.search(keyword, parseInt(page), parseInt(pageSize));

    ctx.body = {
      code: 200,
      message: '搜索成功',
      data: {
        list: result.rows,
        total: result.count,
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        keyword
      }
    };
  }

  // 获取内容详情
  async getDetail(ctx) {
    const { id } = ctx.params;
    const userId = ctx.state.user?.id;

    const content = await Content.findByPk(id, {
      include: [
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name', 'slug']
        },
        {
          model: Tag,
          as: 'tags',
          attributes: ['id', 'name', 'color'],
          through: { attributes: [] }
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'nickname', 'avatar']
        }
      ]
    });

    if (!content || content.status !== 1) {
      throw new NotFoundError('内容不存在');
    }

    // 增加浏览次数
    await content.incrementViewCount();

    // 检查用户是否已解锁
    let isUnlocked = false;
    let isLiked = false;
    let isFavorited = false;

    if (userId) {
      // 检查解锁状态
      if (content.unlockType === 4) { // 免费内容
        isUnlocked = true;
      } else {
        const unlock = await ContentUnlock.findOne({
          where: { userId, contentId: id }
        });
        isUnlocked = !!unlock;

        // 如果是VIP内容，检查VIP状态
        if (!isUnlocked && content.unlockType === 2) {
          const user = await User.findByPk(userId);
          isUnlocked = user.isVip();
        }
      }

      // 检查点赞状态
      const like = await ContentLike.findOne({
        where: { userId, contentId: id }
      });
      isLiked = !!like;

      // 检查收藏状态
      const favorite = await Favorite.findOne({
        where: { userId, contentId: id }
      });
      isFavorited = !!favorite;
    } else {
      // 未登录用户只能查看免费内容
      isUnlocked = content.unlockType === 4;
    }

    // 如果未解锁，隐藏详细内容
    const responseData = content.toJSON();
    if (!isUnlocked) {
      responseData.content = null;
      responseData.netdiskUrl = null;
      responseData.netdiskCode = null;
      responseData.videoUrl = null;
    }

    responseData.isUnlocked = isUnlocked;
    responseData.isLiked = isLiked;
    responseData.isFavorited = isFavorited;

    ctx.body = {
      code: 200,
      message: '获取成功',
      data: responseData
    };
  }

  // 解锁内容
  async unlock(ctx) {
    const { id } = ctx.params;
    const userId = ctx.state.user.id;

    const content = await Content.findByPk(id);
    if (!content || content.status !== 1) {
      throw new NotFoundError('内容不存在');
    }

    // 检查是否已解锁
    const existingUnlock = await ContentUnlock.findOne({
      where: { userId, contentId: id }
    });

    if (existingUnlock) {
      throw new BusinessError('已解锁该内容');
    }

    const user = await User.findByPk(userId);

    // 根据解锁方式处理
    if (content.unlockType === 4) {
      // 免费内容，直接解锁
      await ContentUnlock.create({
        userId,
        contentId: id,
        unlockType: 4,
        unlockPrice: 0,
        ip: ctx.ip
      });
    } else if (content.unlockType === 1) {
      // 积分解锁
      if (user.points < content.unlockPrice) {
        throw new BusinessError('积分不足');
      }

      // 扣除积分
      await user.deductPoints(content.unlockPrice, `解锁内容：${content.title}`);

      // 记录解锁
      await ContentUnlock.create({
        userId,
        contentId: id,
        unlockType: 1,
        unlockPrice: content.unlockPrice,
        ip: ctx.ip
      });

      // 给邀请人奖励（如果有）
      if (user.inviterId) {
        const { PointConfig } = require('../models');
        const inviteConfig = await PointConfig.getByKey('invite_unlock_ratio');
        if (inviteConfig && inviteConfig.ratio) {
          const rewardPoints = Math.floor(content.unlockPrice * inviteConfig.ratio);
          if (rewardPoints > 0) {
            const inviter = await User.findByPk(user.inviterId);
            if (inviter) {
              await inviter.addPoints(rewardPoints, `邀请用户解锁内容奖励`);
            }
          }
        }
      }
    } else if (content.unlockType === 2) {
      // VIP解锁
      if (!user.isVip()) {
        throw new BusinessError('需要VIP权限');
      }

      await ContentUnlock.create({
        userId,
        contentId: id,
        unlockType: 2,
        unlockPrice: 0,
        ip: ctx.ip
      });
    } else {
      throw new BusinessError('不支持的解锁方式');
    }

    // 更新内容解锁次数
    await content.incrementUnlockCount();

    logger.business('解锁内容', userId, { 
      contentId: id, 
      unlockType: content.unlockType,
      unlockPrice: content.unlockPrice 
    });

    ctx.body = {
      code: 200,
      message: '解锁成功',
      data: {
        contentId: id,
        unlockType: content.unlockType,
        unlockPrice: content.unlockPrice,
        remainingPoints: user.points
      }
    };
  }

  // 点赞内容
  async like(ctx) {
    const { id } = ctx.params;
    const userId = ctx.state.user.id;

    // 检查是否已点赞
    const existing = await ContentLike.findOne({
      where: { userId, contentId: id }
    });

    if (existing) {
      throw new BusinessError('已点赞该内容');
    }

    // 添加点赞
    await ContentLike.create({ userId, contentId: id });

    // 更新内容点赞数
    const content = await Content.findByPk(id);
    if (content) {
      await content.incrementLikeCount();
    }

    logger.business('点赞内容', userId, { contentId: id });

    ctx.body = {
      code: 200,
      message: '点赞成功'
    };
  }

  // 取消点赞
  async unlike(ctx) {
    const { id } = ctx.params;
    const userId = ctx.state.user.id;

    const like = await ContentLike.findOne({
      where: { userId, contentId: id }
    });

    if (!like) {
      throw new BusinessError('未点赞该内容');
    }

    await like.destroy();

    // 更新内容点赞数
    const content = await Content.findByPk(id);
    if (content) {
      await content.decrementLikeCount();
    }

    logger.business('取消点赞', userId, { contentId: id });

    ctx.body = {
      code: 200,
      message: '取消点赞成功'
    };
  }

  // 收藏内容
  async favorite(ctx) {
    const { id } = ctx.params;
    const userId = ctx.state.user.id;

    // 检查是否已收藏
    const existing = await Favorite.findOne({
      where: { userId, contentId: id }
    });

    if (existing) {
      throw new BusinessError('已收藏该内容');
    }

    // 添加收藏
    await Favorite.create({ userId, contentId: id });

    // 更新内容收藏数
    const content = await Content.findByPk(id);
    if (content) {
      await content.incrementFavoriteCount();
    }

    logger.business('收藏内容', userId, { contentId: id });

    ctx.body = {
      code: 200,
      message: '收藏成功'
    };
  }

  // 取消收藏
  async unfavorite(ctx) {
    const { id } = ctx.params;
    const userId = ctx.state.user.id;

    const favorite = await Favorite.findOne({
      where: { userId, contentId: id }
    });

    if (!favorite) {
      throw new BusinessError('未收藏该内容');
    }

    await favorite.destroy();

    // 更新内容收藏数
    const content = await Content.findByPk(id);
    if (content) {
      await content.decrementFavoriteCount();
    }

    logger.business('取消收藏', userId, { contentId: id });

    ctx.body = {
      code: 200,
      message: '取消收藏成功'
    };
  }

  // 分享内容
  async share(ctx) {
    const { id } = ctx.params;
    const { shareType = 'wechat' } = ctx.request.body;
    const userId = ctx.state.user.id;

    const content = await Content.findByPk(id);
    if (!content || content.status !== 1) {
      throw new NotFoundError('内容不存在');
    }

    // 记录分享
    await ShareLog.create({
      userId,
      contentId: id,
      shareType,
      ip: ctx.ip
    });

    // 更新内容分享数
    await content.incrementShareCount();

    // TODO: 分享奖励积分（根据配置）

    logger.business('分享内容', userId, { contentId: id, shareType });

    ctx.body = {
      code: 200,
      message: '分享成功',
      data: {
        shareUrl: `${ctx.origin}/content/${id}?from=${userId}`,
        title: content.title,
        description: content.summary
      }
    };
  }

  // 获取评论列表
  async getComments(ctx) {
    const { id } = ctx.params;
    const { page = 1, pageSize = 20 } = ctx.query;
    const offset = (parseInt(page) - 1) * parseInt(pageSize);

    const result = await Comment.findAndCountAll({
      where: { 
        contentId: id, 
        status: 1,
        parentId: null // 只获取顶级评论
      },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'nickname', 'avatar']
        },
        {
          model: Comment,
          as: 'replies',
          include: [
            {
              model: User,
              as: 'user',
              attributes: ['id', 'nickname', 'avatar']
            }
          ],
          limit: 3 // 每个评论最多显示3个回复
        }
      ],
      order: [['createdAt', 'DESC']],
      limit: parseInt(pageSize),
      offset
    });

    ctx.body = {
      code: 200,
      message: '获取成功',
      data: {
        list: result.rows,
        total: result.count,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      }
    };
  }

  // 发表评论
  async addComment(ctx) {
    const { id } = ctx.params;
    const { content, parentId } = ctx.request.body;
    const userId = ctx.state.user.id;

    if (!content || content.trim().length === 0) {
      throw new ValidationError('评论内容不能为空');
    }

    if (content.length > 500) {
      throw new ValidationError('评论内容不能超过500字');
    }

    // 检查内容是否存在
    const targetContent = await Content.findByPk(id);
    if (!targetContent || targetContent.status !== 1) {
      throw new NotFoundError('内容不存在');
    }

    // 如果是回复，检查父评论是否存在
    if (parentId) {
      const parentComment = await Comment.findByPk(parentId);
      if (!parentComment || parentComment.contentId != id) {
        throw new BusinessError('父评论不存在');
      }
    }

    // 创建评论
    const comment = await Comment.create({
      contentId: id,
      userId,
      parentId: parentId || null,
      content: content.trim(),
      ip: ctx.ip
    });

    // 更新内容评论数
    await targetContent.incrementCommentCount();

    // 如果是回复，更新父评论回复数
    if (parentId) {
      const parentComment = await Comment.findByPk(parentId);
      if (parentComment) {
        await parentComment.increment('replyCount');
      }
    }

    // 获取完整的评论信息
    const fullComment = await Comment.findByPk(comment.id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'nickname', 'avatar']
        }
      ]
    });

    logger.business('发表评论', userId, { contentId: id, parentId });

    ctx.body = {
      code: 200,
      message: '评论成功',
      data: fullComment
    };
  }

  // 点赞评论
  async likeComment(ctx) {
    const { commentId } = ctx.params;
    const userId = ctx.state.user.id;

    // 检查是否已点赞
    const existing = await CommentLike.findOne({
      where: { userId, commentId }
    });

    if (existing) {
      throw new BusinessError('已点赞该评论');
    }

    // 添加点赞
    await CommentLike.create({ userId, commentId });

    // 更新评论点赞数
    const comment = await Comment.findByPk(commentId);
    if (comment) {
      await comment.increment('likeCount');
    }

    logger.business('点赞评论', userId, { commentId });

    ctx.body = {
      code: 200,
      message: '点赞成功'
    };
  }

  // 取消评论点赞
  async unlikeComment(ctx) {
    const { commentId } = ctx.params;
    const userId = ctx.state.user.id;

    const like = await CommentLike.findOne({
      where: { userId, commentId }
    });

    if (!like) {
      throw new BusinessError('未点赞该评论');
    }

    await like.destroy();

    // 更新评论点赞数
    const comment = await Comment.findByPk(commentId);
    if (comment) {
      await comment.decrement('likeCount');
    }

    logger.business('取消评论点赞', userId, { commentId });

    ctx.body = {
      code: 200,
      message: '取消点赞成功'
    };
  }

  // 删除评论
  async deleteComment(ctx) {
    const { commentId } = ctx.params;
    const userId = ctx.state.user.id;

    const comment = await Comment.findByPk(commentId);
    if (!comment) {
      throw new NotFoundError('评论不存在');
    }

    // 只能删除自己的评论
    if (comment.userId !== userId) {
      throw new BusinessError('只能删除自己的评论');
    }

    // 软删除
    comment.status = 3; // 已删除
    await comment.save();

    // 更新内容评论数
    const content = await Content.findByPk(comment.contentId);
    if (content) {
      await content.decrementCommentCount();
    }

    logger.business('删除评论', userId, { commentId });

    ctx.body = {
      code: 200,
      message: '删除成功'
    };
  }

  // 获取相关推荐
  async getRelated(ctx) {
    const { id } = ctx.params;
    const { limit = 6 } = ctx.query;

    const content = await Content.findByPk(id, {
      include: [
        {
          model: Tag,
          as: 'tags',
          attributes: ['id']
        }
      ]
    });

    if (!content) {
      throw new NotFoundError('内容不存在');
    }

    // 基于标签和分类推荐相关内容
    const tagIds = content.tags.map(tag => tag.id);
    const { Op } = require('sequelize');

    const relatedContents = await Content.findAll({
      where: {
        id: { [Op.ne]: id }, // 排除当前内容
        status: 1,
        [Op.or]: [
          { categoryId: content.categoryId },
          {
            '$tags.id$': { [Op.in]: tagIds }
          }
        ]
      },
      include: [
        {
          model: Tag,
          as: 'tags',
          attributes: ['id', 'name', 'color'],
          through: { attributes: [] }
        },
        {
          model: Category,
          as: 'category',
          attributes: ['id', 'name']
        }
      ],
      order: [['viewCount', 'DESC'], ['createdAt', 'DESC']],
      limit: parseInt(limit),
      attributes: {
        exclude: ['content']
      }
    });

    ctx.body = {
      code: 200,
      message: '获取成功',
      data: relatedContents
    };
  }

  // ===== 管理员内容管理方法 =====

  // 创建内容（管理员）
  async createContent(ctx) {
    try {
      const {
        title,
        summary,
        content,
        contentType = 1,
        coverImage,
        categoryId,
        unlockType = 1,
        unlockPrice = 0,
        isRecommend = 0,
        isHot = 0,
        status = 1
      } = ctx.request.body;

      const admin = ctx.state.admin;

      if (!title) {
        throw new ValidationError('标题不能为空');
      }

      if (!categoryId) {
        throw new ValidationError('分类不能为空');
      }

      // 验证分类是否存在
      const category = await Category.findByPk(categoryId);
      if (!category) {
        throw new ValidationError('分类不存在');
      }

      const newContent = await Content.create({
        title,
        summary,
        content,
        contentType,
        coverImage,
        categoryId,
        unlockType,
        unlockPrice,
        isRecommend,
        isHot,
        status,
        createdBy: admin.id
      });

      logger.business('管理员创建内容', admin.id, {
        contentId: newContent.id,
        title,
        contentType
      });

      ctx.body = {
        code: 200,
        message: '创建成功',
        data: newContent
      };
    } catch (error) {
      logger.error('创建内容失败', error);
      if (error instanceof ValidationError) {
        throw error;
      }
      throw new BusinessError('创建内容失败');
    }
  }

  // 更新内容（管理员）
  async updateContent(ctx) {
    try {
      const { id } = ctx.params;
      const {
        title,
        summary,
        content,
        contentType,
        coverImage,
        categoryId,
        unlockType,
        unlockPrice,
        isRecommend,
        isHot,
        status
      } = ctx.request.body;

      const admin = ctx.state.admin;

      const existingContent = await Content.findByPk(id);
      if (!existingContent) {
        throw new BusinessError('内容不存在', 404);
      }

      // 如果更新了分类，验证分类是否存在
      if (categoryId && categoryId !== existingContent.categoryId) {
        const category = await Category.findByPk(categoryId);
        if (!category) {
          throw new ValidationError('分类不存在');
        }
      }

      const updateData = {};
      if (title !== undefined) updateData.title = title;
      if (summary !== undefined) updateData.summary = summary;
      if (content !== undefined) updateData.content = content;
      if (contentType !== undefined) updateData.contentType = contentType;
      if (coverImage !== undefined) updateData.coverImage = coverImage;
      if (categoryId !== undefined) updateData.categoryId = categoryId;
      if (unlockType !== undefined) updateData.unlockType = unlockType;
      if (unlockPrice !== undefined) updateData.unlockPrice = unlockPrice;
      if (isRecommend !== undefined) updateData.isRecommend = isRecommend;
      if (isHot !== undefined) updateData.isHot = isHot;
      if (status !== undefined) updateData.status = status;

      await existingContent.update(updateData);

      logger.business('管理员更新内容', admin.id, {
        contentId: id,
        title: existingContent.title
      });

      ctx.body = {
        code: 200,
        message: '更新成功',
        data: existingContent
      };
    } catch (error) {
      logger.error('更新内容失败', error);
      if (error instanceof BusinessError || error instanceof ValidationError) {
        throw error;
      }
      throw new BusinessError('更新内容失败');
    }
  }

  // 删除内容（管理员）
  async deleteContent(ctx) {
    try {
      const { id } = ctx.params;
      const admin = ctx.state.admin;

      const content = await Content.findByPk(id);
      if (!content) {
        throw new BusinessError('内容不存在', 404);
      }

      await content.destroy();

      logger.business('管理员删除内容', admin.id, {
        contentId: id,
        title: content.title
      });

      ctx.body = {
        code: 200,
        message: '删除成功'
      };
    } catch (error) {
      logger.error('删除内容失败', error);
      if (error instanceof BusinessError) {
        throw error;
      }
      throw new BusinessError('删除内容失败');
    }
  }

  // 获取管理员内容列表
  async getAdminContentList(ctx) {
    try {
      const {
        page = 1,
        pageSize = 20,
        categoryId,
        contentType,
        unlockType,
        status,
        keyword,
        sortBy = 'createdAt',
        sortOrder = 'DESC'
      } = ctx.query;

      const offset = (page - 1) * pageSize;
      const where = {};

      // 分类筛选
      if (categoryId) {
        where.categoryId = categoryId;
      }

      // 内容类型筛选
      if (contentType) {
        where.contentType = contentType;
      }

      // 解锁类型筛选
      if (unlockType) {
        where.unlockType = unlockType;
      }

      // 状态筛选
      if (status) {
        where.status = status;
      }

      // 关键词搜索
      if (keyword) {
        where[Op.or] = [
          { title: { [Op.like]: `%${keyword}%` } },
          { summary: { [Op.like]: `%${keyword}%` } }
        ];
      }

      const { count, rows } = await Content.findAndCountAll({
        where,
        include: [
          {
            model: Category,
            as: 'category',
            attributes: ['id', 'name', 'slug']
          },
          {
            model: User,
            as: 'creator',
            attributes: ['id', 'nickname']
          }
        ],
        order: [[sortBy, sortOrder]],
        limit: parseInt(pageSize),
        offset: parseInt(offset),
        attributes: {
          exclude: ['content'] // 列表不返回详细内容
        }
      });

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: {
          contents: rows,
          pagination: {
            page: parseInt(page),
            pageSize: parseInt(pageSize),
            total: count,
            totalPages: Math.ceil(count / pageSize)
          }
        }
      };
    } catch (error) {
      logger.error('获取管理员内容列表失败', error);
      throw new BusinessError('获取内容列表失败');
    }
  }
}

module.exports = new ContentController();
