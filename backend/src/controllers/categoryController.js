const { Category, Content } = require('../models');
const { BusinessError, ValidationError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const { Op } = require('sequelize');

class CategoryController {
  // 获取分类列表
  async getCategories(ctx) {
    try {
      const { includeCount = false } = ctx.query;

      const categories = await Category.findAll({
        where: { status: 1 },
        order: [['sortOrder', 'ASC'], ['createdAt', 'ASC']],
        attributes: includeCount === 'true' ? 
          ['id', 'name', 'slug', 'description', 'icon', 'sortOrder'] : 
          ['id', 'name', 'slug', 'description', 'icon', 'sortOrder']
      });

      // 如果需要包含内容数量
      if (includeCount === 'true') {
        for (const category of categories) {
          const count = await Content.count({
            where: {
              categoryId: category.id,
              status: 1
            }
          });
          category.dataValues.contentCount = count;
        }
      }

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: categories
      };
    } catch (error) {
      logger.error('获取分类列表失败', error);
      throw new BusinessError('获取分类列表失败');
    }
  }

  // 获取分类详情
  async getCategory(ctx) {
    try {
      const { id } = ctx.params;

      const category = await Category.findByPk(id);
      if (!category) {
        throw new BusinessError('分类不存在', 404);
      }

      if (category.status !== 1) {
        throw new BusinessError('分类已禁用', 403);
      }

      // 获取该分类下的内容数量
      const contentCount = await Content.count({
        where: {
          categoryId: category.id,
          status: 1
        }
      });

      const result = category.toJSON();
      result.contentCount = contentCount;

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: result
      };
    } catch (error) {
      logger.error('获取分类详情失败', error);
      if (error instanceof BusinessError) {
        throw error;
      }
      throw new BusinessError('获取分类详情失败');
    }
  }

  // 创建分类（管理员）
  async createCategory(ctx) {
    try {
      const {
        name,
        slug,
        description,
        icon,
        sortOrder = 0,
        status = 1
      } = ctx.request.body;

      const admin = ctx.state.admin;

      if (!name) {
        throw new ValidationError('分类名称不能为空');
      }

      if (!slug) {
        throw new ValidationError('分类标识不能为空');
      }

      // 检查slug是否已存在
      const existingCategory = await Category.findOne({
        where: { slug }
      });

      if (existingCategory) {
        throw new ValidationError('分类标识已存在');
      }

      const newCategory = await Category.create({
        name,
        slug,
        description,
        icon,
        sortOrder,
        status
      });

      logger.business('创建分类', admin.id, {
        categoryId: newCategory.id,
        name,
        slug
      });

      ctx.body = {
        code: 200,
        message: '创建成功',
        data: newCategory
      };
    } catch (error) {
      logger.error('创建分类失败', error);
      if (error instanceof ValidationError) {
        throw error;
      }
      throw new BusinessError('创建分类失败');
    }
  }

  // 更新分类（管理员）
  async updateCategory(ctx) {
    try {
      const { id } = ctx.params;
      const {
        name,
        slug,
        description,
        icon,
        sortOrder,
        status
      } = ctx.request.body;

      const admin = ctx.state.admin;

      const category = await Category.findByPk(id);
      if (!category) {
        throw new BusinessError('分类不存在', 404);
      }

      // 如果更新了slug，检查是否与其他分类冲突
      if (slug && slug !== category.slug) {
        const existingCategory = await Category.findOne({
          where: { 
            slug,
            id: { [Op.ne]: id }
          }
        });

        if (existingCategory) {
          throw new ValidationError('分类标识已存在');
        }
      }

      const updateData = {};
      if (name !== undefined) updateData.name = name;
      if (slug !== undefined) updateData.slug = slug;
      if (description !== undefined) updateData.description = description;
      if (icon !== undefined) updateData.icon = icon;
      if (sortOrder !== undefined) updateData.sortOrder = sortOrder;
      if (status !== undefined) updateData.status = status;

      await category.update(updateData);

      logger.business('更新分类', admin.id, {
        categoryId: id,
        name: category.name
      });

      ctx.body = {
        code: 200,
        message: '更新成功',
        data: category
      };
    } catch (error) {
      logger.error('更新分类失败', error);
      if (error instanceof BusinessError || error instanceof ValidationError) {
        throw error;
      }
      throw new BusinessError('更新分类失败');
    }
  }

  // 删除分类（管理员）
  async deleteCategory(ctx) {
    try {
      const { id } = ctx.params;
      const admin = ctx.state.admin;

      const category = await Category.findByPk(id);
      if (!category) {
        throw new BusinessError('分类不存在', 404);
      }

      // 检查是否有内容使用此分类
      const contentCount = await Content.count({
        where: { categoryId: id }
      });

      if (contentCount > 0) {
        throw new BusinessError('该分类下还有内容，无法删除');
      }

      await category.destroy();

      logger.business('删除分类', admin.id, {
        categoryId: id,
        name: category.name
      });

      ctx.body = {
        code: 200,
        message: '删除成功'
      };
    } catch (error) {
      logger.error('删除分类失败', error);
      if (error instanceof BusinessError) {
        throw error;
      }
      throw new BusinessError('删除分类失败');
    }
  }

  // 获取管理员分类列表
  async getAdminCategories(ctx) {
    try {
      const { 
        page = 1, 
        pageSize = 20, 
        keyword,
        status,
        sortBy = 'sortOrder',
        sortOrder = 'ASC'
      } = ctx.query;

      const offset = (page - 1) * pageSize;
      const where = {};

      // 状态筛选
      if (status) {
        where.status = status;
      }

      // 关键词搜索
      if (keyword) {
        where[Op.or] = [
          { name: { [Op.like]: `%${keyword}%` } },
          { slug: { [Op.like]: `%${keyword}%` } },
          { description: { [Op.like]: `%${keyword}%` } }
        ];
      }

      const { count, rows } = await Category.findAndCountAll({
        where,
        order: [[sortBy, sortOrder]],
        limit: parseInt(pageSize),
        offset: parseInt(offset)
      });

      // 为每个分类添加内容数量
      for (const category of rows) {
        const contentCount = await Content.count({
          where: { categoryId: category.id }
        });
        category.dataValues.contentCount = contentCount;
      }

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: {
          categories: rows,
          pagination: {
            page: parseInt(page),
            pageSize: parseInt(pageSize),
            total: count,
            totalPages: Math.ceil(count / pageSize)
          }
        }
      };
    } catch (error) {
      logger.error('获取管理员分类列表失败', error);
      throw new BusinessError('获取分类列表失败');
    }
  }
}

module.exports = new CategoryController();
