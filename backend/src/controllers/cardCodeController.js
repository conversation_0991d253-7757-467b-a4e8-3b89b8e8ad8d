const { CardCode, User } = require('../models');
const { BusinessError, ValidationError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const crypto = require('crypto');
const { Op } = require('sequelize');

class CardCodeController {
  // 获取卡密列表
  async getCardCodes(ctx) {
    try {
      const { 
        page = 1, 
        pageSize = 20, 
        type, 
        status, 
        keyword 
      } = ctx.query;

      const offset = (page - 1) * pageSize;
      const where = {};

      // 类型筛选
      if (type) {
        where.type = type;
      }

      // 状态筛选
      if (status) {
        where.status = status;
      }

      // 关键词搜索
      if (keyword) {
        where[Op.or] = [
          { code: { [Op.like]: `%${keyword}%` } },
          { description: { [Op.like]: `%${keyword}%` } }
        ];
      }

      const { count, rows } = await CardCode.findAndCountAll({
        where,
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'nickname'],
            required: false
          }
        ],
        order: [['createdAt', 'DESC']],
        limit: parseInt(pageSize),
        offset: parseInt(offset)
      });

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: {
          cardCodes: rows,
          pagination: {
            page: parseInt(page),
            pageSize: parseInt(pageSize),
            total: count,
            totalPages: Math.ceil(count / pageSize)
          }
        }
      };
    } catch (error) {
      logger.error('获取卡密列表失败', error);
      throw new BusinessError('获取卡密列表失败');
    }
  }

  // 生成卡密
  generateCardCode(length = 16) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  // 批量生成卡密
  async batchGenerate(ctx) {
    try {
      const {
        count,
        type,
        value,
        description,
        expiresAt
      } = ctx.request.body;
      const admin = ctx.state.admin;

      if (!count || count <= 0 || count > 1000) {
        throw new ValidationError('生成数量必须在1-1000之间');
      }

      if (!type || ![1, 2, 3].includes(type)) {
        throw new ValidationError('卡密类型无效');
      }

      if (type !== 3 && (!value || value <= 0)) {
        throw new ValidationError('卡密价值必须大于0');
      }

      const cardCodes = [];
      const generatedCodes = new Set();

      // 生成唯一的卡密代码
      for (let i = 0; i < count; i++) {
        let code;
        do {
          code = this.generateCardCode();
        } while (generatedCodes.has(code));
        
        generatedCodes.add(code);
        
        cardCodes.push({
          code,
          type,
          value: value || 0,
          description: description || '',
          status: 1, // 未使用
          expiresAt: expiresAt ? new Date(expiresAt) : null,
          createdBy: admin.id
        });
      }

      // 批量插入数据库
      const createdCards = await CardCode.bulkCreate(cardCodes);

      logger.business('批量生成卡密', admin.id, {
        count,
        type,
        value,
        description
      });

      ctx.body = {
        code: 200,
        message: '生成成功',
        data: {
          count: createdCards.length,
          cardCodes: createdCards
        }
      };
    } catch (error) {
      logger.error('批量生成卡密失败', error);
      if (error instanceof ValidationError) {
        throw error;
      }
      throw new BusinessError('批量生成卡密失败');
    }
  }

  // 删除卡密
  async deleteCardCode(ctx) {
    try {
      const { id } = ctx.params;
      const admin = ctx.state.admin;

      const cardCode = await CardCode.findByPk(id);
      if (!cardCode) {
        throw new BusinessError('卡密不存在', 404);
      }

      if (cardCode.status === 2) {
        throw new BusinessError('已使用的卡密无法删除');
      }

      await cardCode.destroy();

      logger.business('删除卡密', admin.id, {
        cardCodeId: id,
        code: cardCode.code
      });

      ctx.body = {
        code: 200,
        message: '删除成功'
      };
    } catch (error) {
      logger.error('删除卡密失败', error);
      if (error instanceof BusinessError) {
        throw error;
      }
      throw new BusinessError('删除卡密失败');
    }
  }

  // 批量删除卡密
  async batchDelete(ctx) {
    try {
      const { ids } = ctx.request.body;
      const admin = ctx.state.admin;

      if (!Array.isArray(ids) || ids.length === 0) {
        throw new ValidationError('请选择要删除的卡密');
      }

      // 检查是否有已使用的卡密
      const usedCards = await CardCode.count({
        where: {
          id: { [Op.in]: ids },
          status: 2
        }
      });

      if (usedCards > 0) {
        throw new BusinessError('选中的卡密中包含已使用的卡密，无法删除');
      }

      const deletedCount = await CardCode.destroy({
        where: {
          id: { [Op.in]: ids }
        }
      });

      logger.business('批量删除卡密', admin.id, {
        deletedCount,
        ids
      });

      ctx.body = {
        code: 200,
        message: '批量删除成功',
        data: {
          deletedCount
        }
      };
    } catch (error) {
      logger.error('批量删除卡密失败', error);
      if (error instanceof BusinessError || error instanceof ValidationError) {
        throw error;
      }
      throw new BusinessError('批量删除卡密失败');
    }
  }

  // 导出卡密
  async exportCardCodes(ctx) {
    try {
      const { type, status, format = 'txt' } = ctx.query;
      const admin = ctx.state.admin;

      const where = {};
      if (type) where.type = type;
      if (status) where.status = status;

      const cardCodes = await CardCode.findAll({
        where,
        order: [['createdAt', 'DESC']],
        limit: 10000 // 限制导出数量
      });

      if (cardCodes.length === 0) {
        throw new BusinessError('没有符合条件的卡密可导出');
      }

      let content = '';
      let contentType = 'text/plain';
      let filename = `cardcodes_${Date.now()}`;

      if (format === 'csv') {
        contentType = 'text/csv';
        filename += '.csv';
        content = 'Code,Type,Value,Status,Description,CreatedAt,ExpiresAt\n';
        content += cardCodes.map(card => {
          const typeText = ['', '积分卡', '内容解锁卡', '特殊卡'][card.type] || '未知';
          const statusText = ['', '未使用', '已使用', '已过期'][card.status] || '未知';
          return `${card.code},${typeText},${card.value},${statusText},"${card.description}",${card.createdAt},${card.expiresAt || ''}`;
        }).join('\n');
      } else {
        contentType = 'text/plain';
        filename += '.txt';
        content = cardCodes.map(card => card.code).join('\n');
      }

      logger.business('导出卡密', admin.id, {
        count: cardCodes.length,
        format,
        type,
        status
      });

      ctx.set('Content-Type', contentType);
      ctx.set('Content-Disposition', `attachment; filename="${filename}"`);
      ctx.body = content;
    } catch (error) {
      logger.error('导出卡密失败', error);
      if (error instanceof BusinessError) {
        throw error;
      }
      throw new BusinessError('导出卡密失败');
    }
  }

  // 获取卡密统计
  async getStats(ctx) {
    try {
      const stats = await Promise.all([
        // 总数统计
        CardCode.count(),
        // 按状态统计
        CardCode.count({ where: { status: 1 } }), // 未使用
        CardCode.count({ where: { status: 2 } }), // 已使用
        CardCode.count({ where: { status: 3 } }), // 已过期
        // 按类型统计
        CardCode.count({ where: { type: 1 } }), // 积分卡
        CardCode.count({ where: { type: 2 } }), // 内容解锁卡
        CardCode.count({ where: { type: 3 } }), // 特殊卡
        // 今日使用统计
        CardCode.count({
          where: {
            status: 2,
            usedAt: {
              [Op.gte]: new Date(new Date().setHours(0, 0, 0, 0))
            }
          }
        })
      ]);

      const [
        total,
        unused,
        used,
        expired,
        pointCards,
        contentCards,
        specialCards,
        todayUsed
      ] = stats;

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: {
          total,
          byStatus: {
            unused,
            used,
            expired
          },
          byType: {
            pointCards,
            contentCards,
            specialCards
          },
          todayUsed
        }
      };
    } catch (error) {
      logger.error('获取卡密统计失败', error);
      throw new BusinessError('获取卡密统计失败');
    }
  }

  // 使用卡密（用户端）
  async useCardCode(ctx) {
    try {
      const { code } = ctx.request.body;
      const user = ctx.state.user;

      if (!code) {
        throw new ValidationError('卡密不能为空');
      }

      const cardCode = await CardCode.findOne({
        where: { code: code.toUpperCase() }
      });

      if (!cardCode) {
        throw new BusinessError('卡密不存在');
      }

      if (cardCode.status !== 1) {
        throw new BusinessError('卡密已使用或已过期');
      }

      if (cardCode.expiresAt && new Date() > cardCode.expiresAt) {
        // 标记为过期
        await cardCode.update({ status: 3 });
        throw new BusinessError('卡密已过期');
      }

      // 根据卡密类型执行相应操作
      let result = {};
      switch (cardCode.type) {
        case 1: // 积分卡
          await user.addPoints(cardCode.value, `使用积分卡：${code}`);
          result.points = cardCode.value;
          break;
        case 2: // 内容解锁卡
          // 这里可以实现特定内容的解锁逻辑
          result.message = '内容解锁卡使用成功';
          break;
        case 3: // 特殊卡
          result.message = '特殊卡使用成功';
          break;
      }

      // 标记卡密为已使用
      await cardCode.update({
        status: 2,
        usedBy: user.id,
        usedAt: new Date()
      });

      logger.business('使用卡密', user.id, {
        cardCode: code,
        type: cardCode.type,
        value: cardCode.value
      });

      ctx.body = {
        code: 200,
        message: '卡密使用成功',
        data: result
      };
    } catch (error) {
      logger.error('使用卡密失败', error);
      if (error instanceof BusinessError || error instanceof ValidationError) {
        throw error;
      }
      throw new BusinessError('使用卡密失败');
    }
  }
}

module.exports = new CardCodeController();
