const { PointConfig } = require('../models');
const { BusinessError, ValidationError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

class PointConfigController {
  // 获取积分配置列表
  async getConfigs(ctx) {
    try {
      const configs = await PointConfig.findAll({
        order: [['type', 'ASC'], ['createdAt', 'ASC']]
      });

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: configs
      };
    } catch (error) {
      logger.error('获取积分配置失败', error);
      throw new BusinessError('获取积分配置失败');
    }
  }

  // 获取特定类型的积分配置
  async getConfigByType(ctx) {
    try {
      const { type } = ctx.params;

      const config = await PointConfig.findOne({
        where: { type }
      });

      if (!config) {
        throw new BusinessError('配置不存在', 404);
      }

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: config
      };
    } catch (error) {
      logger.error('获取积分配置失败', error);
      if (error instanceof BusinessError) {
        throw error;
      }
      throw new BusinessError('获取积分配置失败');
    }
  }

  // 更新积分配置
  async updateConfig(ctx) {
    try {
      const { type } = ctx.params;
      const { points, description, isEnabled } = ctx.request.body;
      const admin = ctx.state.admin;

      if (points !== undefined && (typeof points !== 'number' || points < 0)) {
        throw new ValidationError('积分值必须是非负数');
      }

      const config = await PointConfig.findOne({
        where: { type }
      });

      if (!config) {
        throw new BusinessError('配置不存在', 404);
      }

      const updateData = {};
      if (points !== undefined) updateData.points = points;
      if (description !== undefined) updateData.description = description;
      if (isEnabled !== undefined) updateData.isEnabled = isEnabled;

      await config.update(updateData);

      logger.business('更新积分配置', admin.id, {
        type,
        oldPoints: config.points,
        newPoints: points,
        isEnabled
      });

      ctx.body = {
        code: 200,
        message: '更新成功',
        data: config
      };
    } catch (error) {
      logger.error('更新积分配置失败', error);
      if (error instanceof BusinessError || error instanceof ValidationError) {
        throw error;
      }
      throw new BusinessError('更新积分配置失败');
    }
  }

  // 创建积分配置
  async createConfig(ctx) {
    try {
      const { type, points, description, isEnabled = true } = ctx.request.body;
      const admin = ctx.state.admin;

      if (!type) {
        throw new ValidationError('配置类型不能为空');
      }

      if (typeof points !== 'number' || points < 0) {
        throw new ValidationError('积分值必须是非负数');
      }

      // 检查是否已存在相同类型的配置
      const existingConfig = await PointConfig.findOne({
        where: { type }
      });

      if (existingConfig) {
        throw new ValidationError('该类型的配置已存在');
      }

      const newConfig = await PointConfig.create({
        type,
        points,
        description,
        isEnabled
      });

      logger.business('创建积分配置', admin.id, {
        type,
        points,
        description
      });

      ctx.body = {
        code: 200,
        message: '创建成功',
        data: newConfig
      };
    } catch (error) {
      logger.error('创建积分配置失败', error);
      if (error instanceof ValidationError) {
        throw error;
      }
      throw new BusinessError('创建积分配置失败');
    }
  }

  // 删除积分配置
  async deleteConfig(ctx) {
    try {
      const { type } = ctx.params;
      const admin = ctx.state.admin;

      const config = await PointConfig.findOne({
        where: { type }
      });

      if (!config) {
        throw new BusinessError('配置不存在', 404);
      }

      await config.destroy();

      logger.business('删除积分配置', admin.id, {
        type,
        points: config.points
      });

      ctx.body = {
        code: 200,
        message: '删除成功'
      };
    } catch (error) {
      logger.error('删除积分配置失败', error);
      if (error instanceof BusinessError) {
        throw error;
      }
      throw new BusinessError('删除积分配置失败');
    }
  }

  // 批量更新积分配置
  async batchUpdateConfigs(ctx) {
    try {
      const { configs } = ctx.request.body;
      const admin = ctx.state.admin;

      if (!Array.isArray(configs) || configs.length === 0) {
        throw new ValidationError('配置列表不能为空');
      }

      const updatePromises = configs.map(async (configData) => {
        const { type, points, description, isEnabled } = configData;

        if (!type) {
          throw new ValidationError('配置类型不能为空');
        }

        if (points !== undefined && (typeof points !== 'number' || points < 0)) {
          throw new ValidationError(`配置 ${type} 的积分值必须是非负数`);
        }

        const config = await PointConfig.findOne({
          where: { type }
        });

        if (config) {
          // 更新现有配置
          const updateData = {};
          if (points !== undefined) updateData.points = points;
          if (description !== undefined) updateData.description = description;
          if (isEnabled !== undefined) updateData.isEnabled = isEnabled;

          await config.update(updateData);
          return config;
        } else {
          // 创建新配置
          return await PointConfig.create({
            type,
            points: points || 0,
            description: description || '',
            isEnabled: isEnabled !== undefined ? isEnabled : true
          });
        }
      });

      const updatedConfigs = await Promise.all(updatePromises);

      logger.business('批量更新积分配置', admin.id, {
        configCount: configs.length,
        types: configs.map(c => c.type)
      });

      ctx.body = {
        code: 200,
        message: '批量更新成功',
        data: updatedConfigs
      };
    } catch (error) {
      logger.error('批量更新积分配置失败', error);
      if (error instanceof ValidationError) {
        throw error;
      }
      throw new BusinessError('批量更新积分配置失败');
    }
  }

  // 重置为默认配置
  async resetToDefault(ctx) {
    try {
      const admin = ctx.state.admin;

      // 默认积分配置
      const defaultConfigs = [
        { type: 'daily_sign', points: 10, description: '每日签到奖励', isEnabled: true },
        { type: 'watch_ad', points: 5, description: '观看广告奖励', isEnabled: true },
        { type: 'invite_user', points: 50, description: '邀请用户奖励', isEnabled: true },
        { type: 'first_share', points: 20, description: '首次分享奖励', isEnabled: true },
        { type: 'complete_profile', points: 30, description: '完善资料奖励', isEnabled: true }
      ];

      // 删除所有现有配置
      await PointConfig.destroy({
        where: {},
        truncate: true
      });

      // 创建默认配置
      const newConfigs = await PointConfig.bulkCreate(defaultConfigs);

      logger.business('重置积分配置为默认值', admin.id, {
        configCount: defaultConfigs.length
      });

      ctx.body = {
        code: 200,
        message: '重置为默认配置成功',
        data: newConfigs
      };
    } catch (error) {
      logger.error('重置积分配置失败', error);
      throw new BusinessError('重置积分配置失败');
    }
  }
}

module.exports = new PointConfigController();
