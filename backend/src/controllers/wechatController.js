const axios = require('axios');
const crypto = require('crypto');
const QRCode = require('qrcode');
const { User, ShareLog } = require('../models');
const config = require('../config/config');
const logger = require('../utils/logger');
const { BusinessError, ValidationError } = require('../middleware/errorHandler');

// 存储扫码登录状态的内存缓存（生产环境建议使用Redis）
const qrLoginCache = new Map();

class WechatController {
  // 生成微信扫码登录二维码
  async generateQRLogin(ctx) {
    try {
      // 生成唯一的登录票据
      const ticket = this.generateTicket();
      const timestamp = Date.now();

      // 构建微信开放平台扫码登录URL
      const { appId, redirectUri } = config.wechat.qrLogin;
      const state = `${ticket}_${timestamp}`;

      const qrLoginUrl = `https://open.weixin.qq.com/connect/qrconnect?` +
        `appid=${appId}&` +
        `redirect_uri=${encodeURIComponent(redirectUri)}&` +
        `response_type=code&` +
        `scope=snsapi_login&` +
        `state=${state}#wechat_redirect`;

      // 生成二维码
      const qrCodeDataURL = await QRCode.toDataURL(qrLoginUrl, {
        width: 300,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      });

      // 存储登录状态
      qrLoginCache.set(ticket, {
        status: 'waiting', // waiting, scanned, confirmed, expired
        timestamp,
        ip: ctx.ip,
        userAgent: ctx.headers['user-agent']
      });

      // 设置过期时间（5分钟）
      setTimeout(() => {
        if (qrLoginCache.has(ticket)) {
          qrLoginCache.set(ticket, { ...qrLoginCache.get(ticket), status: 'expired' });
        }
      }, 5 * 60 * 1000);

      logger.info('生成微信扫码登录二维码', { ticket, ip: ctx.ip });

      ctx.body = {
        code: 200,
        message: '二维码生成成功',
        data: {
          ticket,
          qrCode: qrCodeDataURL,
          qrUrl: qrLoginUrl,
          expiresIn: 300 // 5分钟
        }
      };
    } catch (error) {
      logger.error('生成微信扫码登录二维码失败', error);
      throw new BusinessError('生成登录二维码失败');
    }
  }

  // 检查扫码登录状态
  async checkQRLoginStatus(ctx) {
    const { ticket } = ctx.query;

    if (!ticket) {
      throw new ValidationError('登录票据不能为空');
    }

    const loginInfo = qrLoginCache.get(ticket);

    if (!loginInfo) {
      ctx.body = {
        code: 404,
        message: '登录票据不存在或已过期',
        data: { status: 'expired' }
      };
      return;
    }

    ctx.body = {
      code: 200,
      message: '获取状态成功',
      data: {
        status: loginInfo.status,
        user: loginInfo.user || null,
        token: loginInfo.token || null
      }
    };
  }

  // 微信扫码登录回调处理
  async qrLoginCallback(ctx) {
    const { code, state } = ctx.query;

    if (!code || !state) {
      throw new ValidationError('授权参数不完整');
    }

    try {
      // 解析state获取ticket
      const [ticket] = state.split('_');
      const loginInfo = qrLoginCache.get(ticket);

      if (!loginInfo || loginInfo.status === 'expired') {
        throw new BusinessError('登录已过期，请重新扫码');
      }

      // 获取微信用户信息
      const wechatUser = await this.getWechatUserInfoByCode(code, 'qrLogin');

      // 查找或创建用户
      let user = await User.findByOpenid(wechatUser.openid);

      if (!user) {
        // 创建新用户
        user = await User.create({
          openid: wechatUser.openid,
          unionid: wechatUser.unionid,
          nickname: wechatUser.nickname || '微信用户',
          avatar: wechatUser.headimgurl,
          gender: wechatUser.sex || 0,
          points: config.points?.other?.completeProfile || 0
        });

        logger.business('微信扫码注册新用户', user.id, {
          openid: wechatUser.openid,
          ticket
        });
      }

      // 更新登录信息
      await user.updateLoginInfo(loginInfo.ip);

      // 生成JWT令牌
      const authController = require('./authController');
      const token = authController.generateToken(user);

      // 更新登录状态
      qrLoginCache.set(ticket, {
        ...loginInfo,
        status: 'confirmed',
        user: user.toJSON(),
        token
      });

      logger.business('微信扫码登录成功', user.id, {
        openid: wechatUser.openid,
        ticket,
        ip: loginInfo.ip
      });

      // 返回成功页面或重定向
      ctx.body = `
        <html>
          <head><title>登录成功</title></head>
          <body>
            <div style="text-align: center; padding: 50px;">
              <h2>登录成功</h2>
              <p>您已成功登录，请返回原页面继续操作</p>
              <script>
                // 通知父窗口登录成功
                if (window.opener) {
                  window.opener.postMessage({
                    type: 'wechat_login_success',
                    ticket: '${ticket}',
                    token: '${token}'
                  }, '*');
                  window.close();
                }
              </script>
            </div>
          </body>
        </html>
      `;
    } catch (error) {
      logger.error('微信扫码登录回调处理失败', error);

      ctx.body = `
        <html>
          <head><title>登录失败</title></head>
          <body>
            <div style="text-align: center; padding: 50px;">
              <h2>登录失败</h2>
              <p>${error.message || '登录过程中发生错误，请重试'}</p>
              <script>
                if (window.opener) {
                  window.opener.postMessage({
                    type: 'wechat_login_error',
                    error: '${error.message || '登录失败'}'
                  }, '*');
                  window.close();
                }
              </script>
            </div>
          </body>
        </html>
      `;
    }
  }

  // 微信登录
  async login(ctx) {
    const { code, userInfo, inviteCode } = ctx.request.body;

    if (!code) {
      throw new ValidationError('微信授权码不能为空');
    }

    try {
      // 获取微信用户信息
      const wechatUser = await this.getWechatUserInfo(code);
      
      // 查找或创建用户
      let user = await User.findByOpenid(wechatUser.openid);
      
      if (!user) {
        // 处理邀请关系
        let inviter = null;
        if (inviteCode) {
          inviter = await User.findByInviteCode(inviteCode);
        }

        // 创建新用户
        user = await User.create({
          openid: wechatUser.openid,
          unionid: wechatUser.unionid,
          nickname: userInfo?.nickName || wechatUser.nickname || '微信用户',
          avatar: userInfo?.avatarUrl || wechatUser.headimgurl,
          gender: userInfo?.gender || wechatUser.sex || 0,
          inviterId: inviter ? inviter.id : null,
          points: config.points.other.completeProfile
        });

        // 记录邀请关系
        if (inviter) {
          const { InviteLog } = require('../models');
          await InviteLog.create({
            inviterId: inviter.id,
            inviteeId: user.id,
            rewardPoints: config.points.invite.register
          });

          // 给邀请人奖励积分
          await inviter.addPoints(
            config.points.invite.register,
            `邀请用户${user.nickname}注册`
          );
        }
      } else {
        // 更新用户信息
        if (userInfo) {
          user.nickname = userInfo.nickName || user.nickname;
          user.avatar = userInfo.avatarUrl || user.avatar;
          user.gender = userInfo.gender || user.gender;
          await user.save();
        }
      }

      // 更新登录信息
      await user.updateLoginInfo(ctx.ip);

      // 生成JWT令牌
      const authController = require('./authController');
      const token = authController.generateToken(user);

      logger.business('微信登录', user.id, { 
        openid: wechatUser.openid,
        ip: ctx.ip 
      });

      ctx.body = {
        code: 200,
        message: '登录成功',
        data: {
          user: user.toJSON(),
          token
        }
      };
    } catch (error) {
      logger.error('微信登录失败', error);
      throw new BusinessError('微信登录失败，请重试');
    }
  }

  // 获取微信用户信息
  async getUserInfo(ctx) {
    const { code } = ctx.query;

    if (!code) {
      throw new ValidationError('授权码不能为空');
    }

    try {
      const userInfo = await this.getWechatUserInfo(code);
      
      ctx.body = {
        code: 200,
        message: '获取成功',
        data: userInfo
      };
    } catch (error) {
      logger.error('获取微信用户信息失败', error);
      throw new BusinessError('获取用户信息失败');
    }
  }

  // 创建微信支付订单
  async createPayment(ctx) {
    const { amount, description, orderNo } = ctx.request.body;
    const userId = ctx.state.user.id;

    if (!amount || !description || !orderNo) {
      throw new ValidationError('支付参数不完整');
    }

    try {
      // TODO: 调用微信支付API创建订单
      // 这里简化处理，实际需要调用微信支付接口
      
      const paymentData = {
        orderNo,
        amount,
        description,
        userId,
        prepayId: `prepay_${Date.now()}`,
        paySign: this.generatePaySign(orderNo, amount)
      };

      logger.business('创建支付订单', userId, { orderNo, amount });

      ctx.body = {
        code: 200,
        message: '创建成功',
        data: paymentData
      };
    } catch (error) {
      logger.error('创建支付订单失败', error);
      throw new BusinessError('创建支付订单失败');
    }
  }

  // 微信支付回调
  async paymentNotify(ctx) {
    const { body } = ctx.request;

    try {
      // TODO: 验证微信支付回调签名
      // 处理支付结果
      
      logger.info('收到微信支付回调', body);

      // 返回成功响应给微信
      ctx.body = '<xml><return_code><![CDATA[SUCCESS]]></return_code></xml>';
    } catch (error) {
      logger.error('处理支付回调失败', error);
      ctx.body = '<xml><return_code><![CDATA[FAIL]]></return_code></xml>';
    }
  }

  // 生成分享二维码
  async generateQRCode(ctx) {
    const { type, contentId, inviteCode } = ctx.request.body;
    const userId = ctx.state.user?.id;

    let qrData = '';
    
    if (type === 'invite' && inviteCode) {
      // 邀请二维码
      qrData = `${ctx.origin}/invite/${inviteCode}`;
    } else if (type === 'content' && contentId) {
      // 内容分享二维码
      const shareParam = userId ? `?from=${userId}` : '';
      qrData = `${ctx.origin}/content/${contentId}${shareParam}`;
    } else {
      throw new ValidationError('二维码参数不正确');
    }

    try {
      const qrCodeDataURL = await QRCode.toDataURL(qrData, {
        width: 200,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      });

      ctx.body = {
        code: 200,
        message: '生成成功',
        data: {
          qrCode: qrCodeDataURL,
          url: qrData
        }
      };
    } catch (error) {
      logger.error('生成二维码失败', error);
      throw new BusinessError('生成二维码失败');
    }
  }

  // 微信分享回调
  async shareCallback(ctx) {
    const { contentId, from } = ctx.query;

    if (contentId && from) {
      try {
        // 记录分享点击
        const shareLog = await ShareLog.findOne({
          where: {
            userId: from,
            contentId,
            shareType: 'wechat'
          },
          order: [['createdAt', 'DESC']]
        });

        if (shareLog) {
          await shareLog.increment('clickCount');
          
          // TODO: 根据配置给分享者奖励积分
          
          logger.business('分享点击', from, { contentId });
        }
      } catch (error) {
        logger.error('处理分享回调失败', error);
      }
    }

    // 重定向到内容页面
    ctx.redirect(`/content/${contentId}`);
  }

  // 获取微信用户信息（内部方法）
  async getWechatUserInfo(code) {
    return this.getWechatUserInfoByCode(code, 'miniprogram');
  }

  // 根据授权码获取微信用户信息（支持不同类型）
  async getWechatUserInfoByCode(code, type = 'miniprogram') {
    let appId, appSecret;

    if (type === 'qrLogin') {
      // 微信开放平台扫码登录
      appId = config.wechat.qrLogin.appId;
      appSecret = config.wechat.qrLogin.appSecret;
    } else {
      // 微信小程序或公众号
      appId = config.wechat.appId;
      appSecret = config.wechat.appSecret;
    }

    // 第一步：获取access_token
    const tokenUrl = `https://api.weixin.qq.com/sns/oauth2/access_token?appid=${appId}&secret=${appSecret}&code=${code}&grant_type=authorization_code`;

    const tokenResponse = await axios.get(tokenUrl);
    const tokenData = tokenResponse.data;

    if (tokenData.errcode) {
      throw new Error(`获取access_token失败: ${tokenData.errmsg}`);
    }

    // 第二步：获取用户信息
    const userUrl = `https://api.weixin.qq.com/sns/userinfo?access_token=${tokenData.access_token}&openid=${tokenData.openid}&lang=zh_CN`;

    const userResponse = await axios.get(userUrl);
    const userData = userResponse.data;

    if (userData.errcode) {
      throw new Error(`获取用户信息失败: ${userData.errmsg}`);
    }

    return userData;
  }

  // 生成登录票据
  generateTicket() {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 9);
    return `qr_${timestamp}_${random}`;
  }

  // 生成支付签名（内部方法）
  generatePaySign(orderNo, amount) {
    const timestamp = Math.floor(Date.now() / 1000);
    const nonceStr = Math.random().toString(36).substr(2, 15);
    
    // 简化的签名生成，实际应该按照微信支付规范
    const signStr = `orderNo=${orderNo}&amount=${amount}&timestamp=${timestamp}&nonceStr=${nonceStr}`;
    
    return crypto.createHash('md5').update(signStr).digest('hex');
  }
}

module.exports = new WechatController();
