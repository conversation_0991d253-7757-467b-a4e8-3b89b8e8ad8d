const Router = require('koa-router');
const contentController = require('../controllers/contentController');
const categoryController = require('../controllers/categoryController');
const { adminAuth, checkPermission } = require('../middleware/adminAuth');

const router = new Router();

// 所有路由都需要管理员认证
router.use(adminAuth);

// ===== 内容管理 =====

// 获取管理员内容列表
router.get('/contents', checkPermission('content:read'), contentController.getAdminContentList);

// 获取内容详情
router.get('/contents/:id', checkPermission('content:read'), contentController.getDetail);

// 创建内容
router.post('/contents', checkPermission('content:create'), contentController.createContent);

// 更新内容
router.put('/contents/:id', checkPermission('content:update'), contentController.updateContent);

// 删除内容
router.delete('/contents/:id', checkPermission('content:delete'), contentController.deleteContent);

// ===== 分类管理 =====

// 获取管理员分类列表
router.get('/categories', checkPermission('category:read'), categoryController.getAdminCategories);

// 获取分类详情
router.get('/categories/:id', checkPermission('category:read'), categoryController.getCategory);

// 创建分类
router.post('/categories', checkPermission('category:create'), categoryController.createCategory);

// 更新分类
router.put('/categories/:id', checkPermission('category:update'), categoryController.updateCategory);

// 删除分类
router.delete('/categories/:id', checkPermission('category:delete'), categoryController.deleteCategory);

module.exports = router;
