const Router = require('koa-router');
const contentController = require('../controllers/contentController');
const unlockController = require('../controllers/unlockController');
const { authMiddleware, optionalAuthMiddleware } = require('../middleware/auth');

const router = new Router();

// 获取内容列表
router.get('/', optionalAuthMiddleware, contentController.getList);

// 搜索内容
router.get('/search', optionalAuthMiddleware, contentController.search);

// 获取内容详情
router.get('/:id', optionalAuthMiddleware, contentController.getDetail);

// 解锁内容
router.post('/:id/unlock', authMiddleware, unlockController.unlockContent);

// 检查解锁状态
router.get('/:id/unlock-status', optionalAuthMiddleware, unlockController.checkUnlockStatus);

// 点赞内容
router.post('/:id/like', authMiddleware, contentController.like);

// 取消点赞
router.delete('/:id/like', authMiddleware, contentController.unlike);

// 收藏内容
router.post('/:id/favorite', authMiddleware, contentController.favorite);

// 取消收藏
router.delete('/:id/favorite', authMiddleware, contentController.unfavorite);

// 分享内容
router.post('/:id/share', authMiddleware, contentController.share);

// 获取评论列表
router.get('/:id/comments', contentController.getComments);

// 发表评论
router.post('/:id/comments', contentController.addComment);

// 点赞评论
router.post('/comments/:commentId/like', contentController.likeComment);

// 取消评论点赞
router.delete('/comments/:commentId/like', contentController.unlikeComment);

// 删除评论
router.delete('/comments/:commentId', contentController.deleteComment);

// 获取相关推荐
router.get('/:id/related', contentController.getRelated);

module.exports = router;
