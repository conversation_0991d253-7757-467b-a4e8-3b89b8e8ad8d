const Router = require('koa-router');
const adminController = require('../controllers/adminController');
const { adminAuth, checkPermission, logAdminAction } = require('../middleware/adminAuth');

const router = new Router();

// 管理员登录（不需要认证）
router.post('/login', adminController.login);

// 以下路由需要管理员认证
router.use(adminAuth);

// 管理员信息
router.get('/profile', adminController.getProfile);
router.put('/profile', adminController.updateProfile);

// 用户管理
router.get('/users', adminController.getUsers);
router.get('/users/:id', adminController.getUserDetail);
router.put('/users/:id', adminController.updateUser);
router.post('/users/:id/adjust-points', adminController.adjustUserPoints);
router.post('/users/:id/set-vip', adminController.setUserVip);
router.post('/users/:id/ban', adminController.banUser);

// 内容管理
router.get('/contents', adminController.getContents);
router.post('/contents', adminController.createContent);
router.get('/contents/:id', adminController.getContentDetail);
router.put('/contents/:id', adminController.updateContent);
router.delete('/contents/:id', adminController.deleteContent);
router.post('/contents/:id/recommend', adminController.recommendContent);

// 分类管理
router.get('/categories', adminController.getCategories);
router.post('/categories', adminController.createCategory);
router.put('/categories/:id', adminController.updateCategory);
router.delete('/categories/:id', adminController.deleteCategory);

// 标签管理
router.get('/tags', adminController.getTags);
router.post('/tags', adminController.createTag);
router.put('/tags/:id', adminController.updateTag);
router.delete('/tags/:id', adminController.deleteTag);

// 卡密管理
router.get('/cards', adminController.getCards);
router.post('/cards/batch', adminController.createCardBatch);
router.get('/cards/batches', adminController.getCardBatches);
router.post('/cards/:id/disable', adminController.disableCard);

// 积分配置管理
router.get('/point-config', adminController.getPointConfig);
router.put('/point-config/:key', adminController.updatePointConfig);
router.post('/point-config/batch', adminController.batchUpdatePointConfig);
router.get('/point-config/history', adminController.getPointConfigHistory);

// 评论管理
router.get('/comments', adminController.getComments);
router.put('/comments/:id', adminController.updateComment);
router.delete('/comments/:id', adminController.deleteComment);

// 数据统计
router.get('/stats/overview', adminController.getOverviewStats);
router.get('/stats/users', adminController.getUserStats);
router.get('/stats/contents', adminController.getContentStats);
router.get('/stats/points', adminController.getPointStats);
router.get('/stats/revenue', adminController.getRevenueStats);

// 系统配置
router.get('/system-config', adminController.getSystemConfig);
router.put('/system-config', adminController.updateSystemConfig);

// 操作日志
router.get('/logs', adminController.getAdminLogs);

// 角色权限管理
router.get('/roles', adminController.getRoles);
router.post('/roles', adminController.createRole);
router.put('/roles/:id', adminController.updateRole);
router.delete('/roles/:id', adminController.deleteRole);

module.exports = router;
