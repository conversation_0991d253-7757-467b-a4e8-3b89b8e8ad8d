const Router = require('koa-router');
const userController = require('../controllers/userController');
const unlockController = require('../controllers/unlockController');
const { authMiddleware } = require('../middleware/auth');

const router = new Router();

// 所有用户路由都需要认证
router.use(authMiddleware);

// 获取用户信息
router.get('/profile', userController.getProfile);

// 更新用户信息
router.put('/profile', userController.updateProfile);

// 获取积分信息
router.get('/points', userController.getPoints);

// 获取积分日志
router.get('/point-logs', userController.getPointLogs);

// 获取解锁记录
router.get('/unlocks', unlockController.getUserUnlocks);

// 获取收藏列表
router.get('/favorites', userController.getFavorites);

// 添加收藏
router.post('/favorites', userController.addFavorite);

// 取消收藏
router.delete('/favorites/:contentId', userController.removeFavorite);

// 获取邀请信息
router.get('/invite', userController.getInviteInfo);

// 获取邀请记录
router.get('/invite-logs', userController.getInviteLogs);

// 获取签到信息
router.get('/sign-info', userController.getSignInfo);

// 获取VIP信息
router.get('/vip-info', userController.getVipInfo);

module.exports = router;
