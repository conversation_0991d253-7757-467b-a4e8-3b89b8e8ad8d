const Router = require('koa-router');
const wechatController = require('../controllers/wechatController');

const router = new Router();

// 微信登录
router.post('/login', wechatController.login);

// 生成微信扫码登录二维码
router.post('/qr-login', wechatController.generateQRLogin);

// 检查扫码登录状态
router.get('/qr-login-status', wechatController.checkQRLoginStatus);

// 微信扫码登录回调
router.get('/qr-login-callback', wechatController.qrLoginCallback);

// 获取微信用户信息
router.get('/userinfo', wechatController.getUserInfo);

// 微信支付
router.post('/pay', wechatController.createPayment);

// 微信支付回调
router.post('/notify', wechatController.paymentNotify);

// 生成分享二维码
router.post('/qrcode', wechatController.generateQRCode);

// 微信分享回调
router.get('/share-callback', wechatController.shareCallback);

module.exports = router;
