const Router = require('koa-router');
const logger = require('../utils/logger');

// 导入各模块路由
const authRoutes = require('./auth');
const userRoutes = require('./user');
const contentRoutes = require('./content');
const categoryRoutes = require('./category');
const pointRoutes = require('./point');
const adminRoutes = require('./admin');
const adminContentRoutes = require('./admin-content');
const publicRoutes = require('./public');
const wechatRoutes = require('./wechat');

const router = new Router({
  prefix: '/api/v1'
});

// 请求日志中间件
router.use(async (ctx, next) => {
  const start = Date.now();
  
  try {
    await next();
    const responseTime = Date.now() - start;
    logger.request(ctx, responseTime);
  } catch (error) {
    const responseTime = Date.now() - start;
    logger.request(ctx, responseTime);
    throw error;
  }
});

// 注册路由
router.use('/auth', authRoutes.routes(), authRoutes.allowedMethods());
router.use('/user', userRoutes.routes(), userRoutes.allowedMethods());
router.use('/content', contentRoutes.routes(), contentRoutes.allowedMethods());
router.use('/categories', categoryRoutes.routes(), categoryRoutes.allowedMethods());
router.use('/point', pointRoutes.routes(), pointRoutes.allowedMethods());
router.use('/admin', adminRoutes.routes(), adminRoutes.allowedMethods());
router.use('/admin', adminContentRoutes.routes(), adminContentRoutes.allowedMethods());
router.use('/public', publicRoutes.routes(), publicRoutes.allowedMethods());
router.use('/wechat', wechatRoutes.routes(), wechatRoutes.allowedMethods());

// API文档路由
router.get('/docs', async (ctx) => {
  ctx.body = {
    title: '微信知识付费平台 API 文档',
    version: '1.0.0',
    description: '提供用户管理、内容管理、积分系统等功能的RESTful API',
    endpoints: {
      auth: {
        'POST /auth/login': '用户登录',
        'POST /auth/register': '用户注册',
        'POST /auth/refresh': '刷新令牌',
        'POST /auth/logout': '用户登出'
      },
      user: {
        'GET /user/profile': '获取用户信息',
        'PUT /user/profile': '更新用户信息',
        'GET /user/points': '获取积分信息',
        'GET /user/unlocks': '获取解锁记录',
        'GET /user/favorites': '获取收藏列表'
      },
      content: {
        'GET /content': '获取内容列表',
        'GET /content/:id': '获取内容详情',
        'POST /content/:id/unlock': '解锁内容',
        'POST /content/:id/like': '点赞内容',
        'POST /content/:id/favorite': '收藏内容',
        'GET /content/:id/comments': '获取评论列表',
        'POST /content/:id/comments': '发表评论'
      },
      point: {
        'POST /point/sign': '每日签到',
        'POST /point/watch-ad': '观看广告',
        'POST /point/card-exchange': '卡密兑换',
        'GET /point/logs': '积分日志',
        'GET /point/config': '积分配置'
      },
      admin: {
        'POST /admin/login': '管理员登录',
        'GET /admin/users': '用户管理',
        'GET /admin/contents': '内容管理',
        'GET /admin/point-config': '积分配置管理',
        'GET /admin/stats': '数据统计'
      },
      public: {
        'GET /public/categories': '获取分类列表',
        'GET /public/tags': '获取标签列表',
        'GET /public/hot': '获取热门内容',
        'GET /public/recommend': '获取推荐内容'
      },
      wechat: {
        'POST /wechat/login': '微信登录',
        'GET /wechat/userinfo': '获取微信用户信息',
        'POST /wechat/notify': '微信支付回调'
      }
    },
    authentication: {
      type: 'Bearer Token',
      header: 'Authorization: Bearer <token>',
      description: '除公开接口外，所有接口都需要在请求头中携带有效的JWT令牌'
    },
    responseFormat: {
      success: {
        code: 200,
        message: 'success',
        data: {},
        timestamp: '2023-12-01T00:00:00.000Z'
      },
      error: {
        code: 400,
        error: 'ERROR_CODE',
        message: 'Error message',
        timestamp: '2023-12-01T00:00:00.000Z'
      }
    }
  };
});

module.exports = router;
