const axios = require('axios');
const logger = require('./src/utils/logger');

const baseURL = 'http://localhost:3000/api/v1';

// 配置axios以绕过代理
const axiosInstance = axios.create({
  baseURL,
  proxy: false,
  timeout: 10000
});

async function testPointsAndCards() {
  try {
    logger.info('开始测试积分配置和卡密管理功能...');

    // 首先获取管理员令牌
    logger.info('步骤1: 管理员登录');
    let adminToken = null;
    
    try {
      const loginResponse = await axiosInstance.post('/admin/login', {
        username: 'admin',
        password: 'admin123'
      });

      if (loginResponse.data.code === 200) {
        adminToken = loginResponse.data.data.token;
        logger.info(`✅ 管理员登录成功，令牌: ${adminToken.substring(0, 20)}...`);
      }
    } catch (error) {
      logger.error(`❌ 管理员登录失败: ${error.response?.data?.message || error.message}`);
      return;
    }

    // 测试积分配置功能
    logger.info('步骤2: 测试积分配置功能');
    
    // 获取积分配置列表
    try {
      const configsResponse = await axiosInstance.get('/admin/point-configs', {
        headers: {
          'Authorization': `Bearer ${adminToken}`
        }
      });

      if (configsResponse.data.code === 200) {
        const configs = configsResponse.data.data;
        logger.info(`✅ 获取积分配置成功，共 ${configs.length} 个配置`);
        configs.forEach(config => {
          logger.info(`  - ${config.type}: ${config.points} 积分 (${config.description})`);
        });
      }
    } catch (error) {
      logger.error(`❌ 获取积分配置失败: ${error.response?.data?.message || error.message}`);
    }

    // 更新积分配置
    try {
      const updateResponse = await axiosInstance.put('/admin/point-configs/daily_sign', {
        points: 15,
        description: '每日签到奖励（已更新）',
        isEnabled: true
      }, {
        headers: {
          'Authorization': `Bearer ${adminToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (updateResponse.data.code === 200) {
        logger.info(`✅ 更新积分配置成功`);
        logger.info(`  - 每日签到奖励更新为: ${updateResponse.data.data.points} 积分`);
      }
    } catch (error) {
      logger.error(`❌ 更新积分配置失败: ${error.response?.data?.message || error.message}`);
    }

    // 批量更新积分配置
    try {
      const batchUpdateResponse = await axiosInstance.put('/admin/point-configs', {
        configs: [
          { type: 'watch_ad', points: 8, description: '观看广告奖励（批量更新）', isEnabled: true },
          { type: 'invite_user', points: 60, description: '邀请用户奖励（批量更新）', isEnabled: true }
        ]
      }, {
        headers: {
          'Authorization': `Bearer ${adminToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (batchUpdateResponse.data.code === 200) {
        logger.info(`✅ 批量更新积分配置成功`);
      }
    } catch (error) {
      logger.error(`❌ 批量更新积分配置失败: ${error.response?.data?.message || error.message}`);
    }

    // 测试卡密管理功能
    logger.info('步骤3: 测试卡密管理功能');

    // 获取卡密统计
    try {
      const statsResponse = await axiosInstance.get('/admin/card-codes/stats', {
        headers: {
          'Authorization': `Bearer ${adminToken}`
        }
      });

      if (statsResponse.data.code === 200) {
        const stats = statsResponse.data.data;
        logger.info(`✅ 获取卡密统计成功`);
        logger.info(`  - 总卡密数: ${stats.total}`);
        logger.info(`  - 未使用: ${stats.byStatus.unused}`);
        logger.info(`  - 已使用: ${stats.byStatus.used}`);
        logger.info(`  - 已过期: ${stats.byStatus.expired}`);
        logger.info(`  - 积分卡: ${stats.byType.pointCards}`);
        logger.info(`  - 内容解锁卡: ${stats.byType.contentCards}`);
      }
    } catch (error) {
      logger.error(`❌ 获取卡密统计失败: ${error.response?.data?.message || error.message}`);
    }

    // 批量生成卡密
    try {
      const generateResponse = await axiosInstance.post('/admin/card-codes/batch-generate', {
        count: 5,
        type: 1, // 积分卡
        value: 50,
        description: '测试积分卡'
      }, {
        headers: {
          'Authorization': `Bearer ${adminToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (generateResponse.data.code === 200) {
        const result = generateResponse.data.data;
        logger.info(`✅ 批量生成卡密成功，生成 ${result.count} 个卡密`);
        result.cardCodes.slice(0, 3).forEach(card => {
          logger.info(`  - ${card.code} (价值: ${card.value} 积分)`);
        });
      }
    } catch (error) {
      logger.error(`❌ 批量生成卡密失败: ${error.response?.data?.message || error.message}`);
    }

    // 获取卡密列表
    let cardCodes = [];
    try {
      const listResponse = await axiosInstance.get('/admin/card-codes', {
        headers: {
          'Authorization': `Bearer ${adminToken}`
        }
      });

      if (listResponse.data.code === 200) {
        cardCodes = listResponse.data.data.cardCodes;
        logger.info(`✅ 获取卡密列表成功，共 ${cardCodes.length} 个卡密`);
      }
    } catch (error) {
      logger.error(`❌ 获取卡密列表失败: ${error.response?.data?.message || error.message}`);
    }

    // 删除一个未使用的卡密
    if (cardCodes.length > 0) {
      const unusedCard = cardCodes.find(card => card.status === 1);
      if (unusedCard) {
        try {
          const deleteResponse = await axiosInstance.delete(`/admin/card-codes/${unusedCard.id}`, {
            headers: {
              'Authorization': `Bearer ${adminToken}`
            }
          });

          if (deleteResponse.data.code === 200) {
            logger.info(`✅ 删除卡密成功: ${unusedCard.code}`);
          }
        } catch (error) {
          logger.error(`❌ 删除卡密失败: ${error.response?.data?.message || error.message}`);
        }
      }
    }

    // 测试权限验证
    logger.info('步骤4: 测试权限验证');
    
    try {
      // 测试无效令牌
      const invalidResponse = await axiosInstance.get('/admin/point-configs', {
        headers: {
          'Authorization': 'Bearer invalid_token'
        }
      });
    } catch (error) {
      if (error.response?.status === 401) {
        logger.info('✅ 无效令牌被正确拒绝');
      }
    }

    try {
      // 测试无令牌访问
      const noTokenResponse = await axiosInstance.get('/admin/card-codes/stats');
    } catch (error) {
      if (error.response?.status === 401) {
        logger.info('✅ 无令牌访问被正确拒绝');
      }
    }

    logger.info('🎉 积分配置和卡密管理功能测试完成！');
    logger.info('');
    logger.info('📊 测试总结:');
    logger.info('✅ 积分配置获取正常');
    logger.info('✅ 积分配置更新正常');
    logger.info('✅ 积分配置批量更新正常');
    logger.info('✅ 卡密统计获取正常');
    logger.info('✅ 卡密批量生成正常');
    logger.info('✅ 卡密列表获取正常');
    logger.info('✅ 卡密删除功能正常');
    logger.info('✅ 权限验证机制正常');
    logger.info('');
    logger.info('🌐 管理界面访问地址:');
    logger.info('1. 积分配置与卡密管理: http://localhost:3000/admin-points-cards.html');
    logger.info('2. 管理后台主页: http://localhost:3000/admin-dashboard.html');
    logger.info('3. 管理员登录: http://localhost:3000/admin-login.html');
    logger.info('');
    logger.info('💡 功能特性:');
    logger.info('- 积分获取规则配置');
    logger.info('- 卡密批量生成和管理');
    logger.info('- 卡密使用统计');
    logger.info('- 权限验证和安全控制');
    logger.info('- 响应式管理界面');

  } catch (error) {
    logger.error('❌ 积分配置和卡密管理测试过程中发生错误:', error.message);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  testPointsAndCards().then(() => {
    logger.info('积分配置和卡密管理测试完成');
    process.exit(0);
  }).catch((error) => {
    logger.error('积分配置和卡密管理测试失败:', error);
    process.exit(1);
  });
}

module.exports = {
  testPointsAndCards
};
