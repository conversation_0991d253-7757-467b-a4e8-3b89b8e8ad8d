<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>内容管理系统演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .title {
            color: #333;
            margin-bottom: 30px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .section {
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }
        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .form-control:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
        }
        .btn-success:hover {
            background-color: #1e7e34;
        }
        .btn-danger {
            background-color: #dc3545;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .btn-secondary {
            background-color: #6c757d;
        }
        .btn-secondary:hover {
            background-color: #545b62;
        }
        .content-list {
            margin-top: 20px;
        }
        .content-item {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 10px;
            background-color: #f8f9fa;
        }
        .content-item h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .content-item p {
            margin: 5px 0;
            color: #666;
            font-size: 14px;
        }
        .content-actions {
            margin-top: 10px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        .tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            color: #666;
        }
        .tab.active {
            color: #007bff;
            border-bottom-color: #007bff;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">内容管理系统演示</h1>
        
        <div class="tabs">
            <div class="tab active" onclick="switchTab('content')">内容管理</div>
            <div class="tab" onclick="switchTab('category')">分类管理</div>
            <div class="tab" onclick="switchTab('unlock')">解锁测试</div>
        </div>

        <!-- 内容管理 -->
        <div id="contentTab" class="tab-content active">
            <div class="grid">
                <div class="section">
                    <h3>创建内容</h3>
                    <form id="contentForm">
                        <div class="form-group">
                            <label>标题</label>
                            <input type="text" class="form-control" id="contentTitle" required>
                        </div>
                        <div class="form-group">
                            <label>摘要</label>
                            <textarea class="form-control" id="contentSummary" rows="3"></textarea>
                        </div>
                        <div class="form-group">
                            <label>内容</label>
                            <textarea class="form-control" id="contentBody" rows="5"></textarea>
                        </div>
                        <div class="form-group">
                            <label>内容类型</label>
                            <select class="form-control" id="contentType">
                                <option value="1">文章</option>
                                <option value="2">网盘资源</option>
                                <option value="3">视频</option>
                                <option value="4">文档</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>分类ID</label>
                            <input type="number" class="form-control" id="categoryId" value="1">
                        </div>
                        <div class="form-group">
                            <label>解锁方式</label>
                            <select class="form-control" id="unlockType">
                                <option value="1">积分解锁</option>
                                <option value="2">VIP解锁</option>
                                <option value="3">卡密解锁</option>
                                <option value="4">免费</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>解锁价格（积分）</label>
                            <input type="number" class="form-control" id="unlockPrice" value="0">
                        </div>
                        <button type="submit" class="btn btn-success">创建内容</button>
                    </form>
                </div>

                <div class="section">
                    <h3>内容列表</h3>
                    <button class="btn" onclick="loadContents()">刷新列表</button>
                    <div id="contentList" class="content-list">
                        <div class="loading">点击刷新列表加载内容</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分类管理 -->
        <div id="categoryTab" class="tab-content">
            <div class="grid">
                <div class="section">
                    <h3>创建分类</h3>
                    <form id="categoryForm">
                        <div class="form-group">
                            <label>分类名称</label>
                            <input type="text" class="form-control" id="categoryName" required>
                        </div>
                        <div class="form-group">
                            <label>分类标识</label>
                            <input type="text" class="form-control" id="categorySlug" required>
                        </div>
                        <div class="form-group">
                            <label>描述</label>
                            <textarea class="form-control" id="categoryDescription" rows="3"></textarea>
                        </div>
                        <div class="form-group">
                            <label>排序</label>
                            <input type="number" class="form-control" id="sortOrder" value="0">
                        </div>
                        <button type="submit" class="btn btn-success">创建分类</button>
                    </form>
                </div>

                <div class="section">
                    <h3>分类列表</h3>
                    <button class="btn" onclick="loadCategories()">刷新列表</button>
                    <div id="categoryList" class="content-list">
                        <div class="loading">点击刷新列表加载分类</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 解锁测试 -->
        <div id="unlockTab" class="tab-content">
            <div class="section">
                <h3>内容解锁测试</h3>
                <p class="status info">
                    <strong>说明：</strong>此功能需要用户登录。请先通过微信扫码登录或手机号登录获取用户令牌。
                </p>
                <div class="form-group">
                    <label>用户令牌（JWT Token）</label>
                    <input type="text" class="form-control" id="userToken" placeholder="请输入用户登录后获得的JWT令牌">
                </div>
                <div class="form-group">
                    <label>内容ID</label>
                    <input type="number" class="form-control" id="unlockContentId" placeholder="要解锁的内容ID">
                </div>
                <div class="form-group">
                    <label>卡密（仅卡密解锁时需要）</label>
                    <input type="text" class="form-control" id="cardCode" placeholder="卡密代码">
                </div>
                <button class="btn" onclick="checkUnlockStatus()">检查解锁状态</button>
                <button class="btn btn-success" onclick="unlockContent()">解锁内容</button>
                <button class="btn btn-secondary" onclick="getUserUnlocks()">获取解锁记录</button>
                
                <div id="unlockResult" class="content-list">
                    <!-- 解锁结果将显示在这里 -->
                </div>
            </div>
        </div>

        <div id="statusMessage"></div>
    </div>

    <script>
        const API_BASE = '/api/v1';
        let adminToken = null;

        // 切换标签页
        function switchTab(tabName) {
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有标签的激活状态
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的标签内容
            document.getElementById(tabName + 'Tab').classList.add('active');
            
            // 激活选中的标签
            event.target.classList.add('active');
        }

        // 显示状态消息
        function showStatus(message, type = 'info') {
            const statusEl = document.getElementById('statusMessage');
            statusEl.className = `status ${type}`;
            statusEl.textContent = message;
            statusEl.style.display = 'block';
            
            setTimeout(() => {
                statusEl.style.display = 'none';
            }, 5000);
        }

        // 创建内容
        document.getElementById('contentForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = {
                title: document.getElementById('contentTitle').value,
                summary: document.getElementById('contentSummary').value,
                content: document.getElementById('contentBody').value,
                contentType: parseInt(document.getElementById('contentType').value),
                categoryId: parseInt(document.getElementById('categoryId').value),
                unlockType: parseInt(document.getElementById('unlockType').value),
                unlockPrice: parseInt(document.getElementById('unlockPrice').value)
            };

            try {
                const response = await fetch(`${API_BASE}/admin/contents`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${adminToken || 'demo_admin_token'}`
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();
                
                if (result.code === 200) {
                    showStatus('内容创建成功！', 'success');
                    document.getElementById('contentForm').reset();
                    loadContents();
                } else {
                    showStatus(`创建失败：${result.message}`, 'error');
                }
            } catch (error) {
                showStatus(`创建失败：${error.message}`, 'error');
            }
        });

        // 加载内容列表
        async function loadContents() {
            try {
                const response = await fetch(`${API_BASE}/content`);
                const result = await response.json();
                
                if (result.code === 200) {
                    displayContents(result.data.contents || []);
                } else {
                    showStatus(`加载失败：${result.message}`, 'error');
                }
            } catch (error) {
                showStatus(`加载失败：${error.message}`, 'error');
            }
        }

        // 显示内容列表
        function displayContents(contents) {
            const listEl = document.getElementById('contentList');
            
            if (contents.length === 0) {
                listEl.innerHTML = '<div class="loading">暂无内容</div>';
                return;
            }

            listEl.innerHTML = contents.map(content => `
                <div class="content-item">
                    <h4>${content.title}</h4>
                    <p><strong>ID:</strong> ${content.id}</p>
                    <p><strong>摘要:</strong> ${content.summary || '无'}</p>
                    <p><strong>类型:</strong> ${getContentTypeText(content.contentType)}</p>
                    <p><strong>解锁方式:</strong> ${getUnlockTypeText(content.unlockType)}</p>
                    <p><strong>价格:</strong> ${content.unlockPrice || 0} 积分</p>
                    <p><strong>浏览次数:</strong> ${content.viewCount || 0}</p>
                    <p><strong>解锁次数:</strong> ${content.unlockCount || 0}</p>
                    <div class="content-actions">
                        <button class="btn btn-danger" onclick="deleteContent(${content.id})">删除</button>
                    </div>
                </div>
            `).join('');
        }

        // 获取内容类型文本
        function getContentTypeText(type) {
            const types = { 1: '文章', 2: '网盘资源', 3: '视频', 4: '文档' };
            return types[type] || '未知';
        }

        // 获取解锁类型文本
        function getUnlockTypeText(type) {
            const types = { 1: '积分解锁', 2: 'VIP解锁', 3: '卡密解锁', 4: '免费' };
            return types[type] || '未知';
        }

        // 删除内容
        async function deleteContent(contentId) {
            if (!confirm('确定要删除这个内容吗？')) {
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/admin/contents/${contentId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${adminToken || 'demo_admin_token'}`
                    }
                });

                const result = await response.json();
                
                if (result.code === 200) {
                    showStatus('删除成功！', 'success');
                    loadContents();
                } else {
                    showStatus(`删除失败：${result.message}`, 'error');
                }
            } catch (error) {
                showStatus(`删除失败：${error.message}`, 'error');
            }
        }

        // 页面加载时的初始化
        document.addEventListener('DOMContentLoaded', () => {
            showStatus('内容管理系统演示页面已加载。注意：某些功能需要有效的管理员令牌。', 'info');
        });
    </script>
</body>
</html>
