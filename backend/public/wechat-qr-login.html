<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信扫码登录演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .title {
            color: #333;
            margin-bottom: 30px;
        }
        .qr-container {
            margin: 20px 0;
            padding: 20px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .qr-code {
            max-width: 300px;
            margin: 0 auto;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 6px;
            font-weight: 500;
        }
        .status.waiting {
            background-color: #e3f2fd;
            color: #1976d2;
            border: 1px solid #bbdefb;
        }
        .status.scanned {
            background-color: #f3e5f5;
            color: #7b1fa2;
            border: 1px solid #ce93d8;
        }
        .status.success {
            background-color: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #a5d6a7;
        }
        .status.error {
            background-color: #ffebee;
            color: #c62828;
            border: 1px solid #ef9a9a;
        }
        .status.expired {
            background-color: #fff3e0;
            color: #ef6c00;
            border: 1px solid #ffcc02;
        }
        .btn {
            background-color: #07c160;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .btn:hover {
            background-color: #06ad56;
        }
        .btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .user-info {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 6px;
            text-align: left;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #07c160;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">微信扫码登录演示</h1>
        
        <div id="qrContainer" class="qr-container" style="display: none;">
            <p>请使用微信扫描下方二维码登录</p>
            <img id="qrCode" class="qr-code" alt="微信登录二维码">
            <p style="color: #666; font-size: 14px; margin-top: 15px;">
                二维码有效期5分钟，过期请重新获取
            </p>
        </div>

        <div id="status" class="status waiting" style="display: none;">
            <span class="loading"></span> 等待扫码...
        </div>

        <div id="userInfo" class="user-info" style="display: none;">
            <h3>登录成功！</h3>
            <p><strong>用户昵称：</strong><span id="userNickname"></span></p>
            <p><strong>用户ID：</strong><span id="userId"></span></p>
            <p><strong>登录令牌：</strong><span id="userToken" style="word-break: break-all; font-family: monospace; font-size: 12px;"></span></p>
        </div>

        <div>
            <button id="generateBtn" class="btn" onclick="generateQRCode()">生成登录二维码</button>
            <button id="refreshBtn" class="btn" onclick="refreshQRCode()" style="display: none;">刷新二维码</button>
        </div>

        <div id="simulateContainer" style="display: none; margin-top: 20px; padding: 20px; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px;">
            <h4 style="margin-top: 0; color: #856404;">模拟扫码操作（演示用）</h4>
            <p style="color: #856404; font-size: 14px;">在实际环境中，用户会使用微信扫描二维码</p>
            <button class="btn" onclick="simulateScan()" style="background-color: #ffc107; color: #212529;">模拟扫码</button>
            <button class="btn" onclick="simulateConfirm()" style="background-color: #28a745;">模拟确认登录</button>
        </div>

        <div style="margin-top: 30px; padding: 20px; background-color: #f8f9fa; border-radius: 6px; text-align: left;">
            <h3>使用说明：</h3>
            <ol>
                <li>点击"生成登录二维码"按钮</li>
                <li>使用微信扫描生成的二维码</li>
                <li>在微信中确认登录</li>
                <li>页面将自动显示登录结果</li>
            </ol>
            <p style="color: #666; font-size: 14px; margin-top: 15px;">
                <strong>注意：</strong>此功能需要配置微信开放平台的网站应用，并设置正确的回调域名。
            </p>
        </div>
    </div>

    <script>
        let currentTicket = null;
        let statusCheckInterval = null;

        async function generateQRCode() {
            try {
                document.getElementById('generateBtn').disabled = true;
                document.getElementById('generateBtn').textContent = '生成中...';
                
                // 隐藏之前的结果
                document.getElementById('qrContainer').style.display = 'none';
                document.getElementById('status').style.display = 'none';
                document.getElementById('userInfo').style.display = 'none';
                document.getElementById('refreshBtn').style.display = 'none';

                const response = await fetch('/api/v1/wechat/qr-login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (result.code === 200) {
                    currentTicket = result.data.ticket;
                    
                    // 显示二维码
                    document.getElementById('qrCode').src = result.data.qrCode;
                    document.getElementById('qrContainer').style.display = 'block';
                    
                    // 显示状态
                    updateStatus('waiting', '等待扫码...');
                    
                    // 开始检查登录状态
                    startStatusCheck();
                    
                    // 显示刷新按钮和模拟操作
                    document.getElementById('refreshBtn').style.display = 'inline-block';
                    document.getElementById('simulateContainer').style.display = 'block';
                } else {
                    updateStatus('error', '生成二维码失败：' + result.message);
                }
            } catch (error) {
                console.error('生成二维码失败:', error);
                updateStatus('error', '生成二维码失败，请检查网络连接');
            } finally {
                document.getElementById('generateBtn').disabled = false;
                document.getElementById('generateBtn').textContent = '生成登录二维码';
            }
        }

        function startStatusCheck() {
            if (statusCheckInterval) {
                clearInterval(statusCheckInterval);
            }

            statusCheckInterval = setInterval(async () => {
                if (!currentTicket) return;

                try {
                    const response = await fetch(`/api/v1/wechat/qr-login-status?ticket=${currentTicket}`);
                    const result = await response.json();

                    if (result.code === 200) {
                        const status = result.data.status;
                        
                        switch (status) {
                            case 'waiting':
                                updateStatus('waiting', '等待扫码...');
                                break;
                            case 'scanned':
                                updateStatus('scanned', '已扫码，请在微信中确认登录');
                                break;
                            case 'confirmed':
                                updateStatus('success', '登录成功！');
                                showUserInfo(result.data.user, result.data.token);
                                stopStatusCheck();
                                break;
                            case 'expired':
                                updateStatus('expired', '二维码已过期，请重新生成');
                                stopStatusCheck();
                                break;
                        }
                    } else if (result.code === 404) {
                        updateStatus('expired', '登录已过期，请重新生成二维码');
                        stopStatusCheck();
                    }
                } catch (error) {
                    console.error('检查登录状态失败:', error);
                }
            }, 2000); // 每2秒检查一次
        }

        function stopStatusCheck() {
            if (statusCheckInterval) {
                clearInterval(statusCheckInterval);
                statusCheckInterval = null;
            }
        }

        function updateStatus(type, message) {
            const statusEl = document.getElementById('status');
            statusEl.className = `status ${type}`;
            
            if (type === 'waiting') {
                statusEl.innerHTML = `<span class="loading"></span> ${message}`;
            } else {
                statusEl.textContent = message;
            }
            
            statusEl.style.display = 'block';
        }

        function showUserInfo(user, token) {
            document.getElementById('userNickname').textContent = user.nickname || '未知';
            document.getElementById('userId').textContent = user.id || '未知';
            document.getElementById('userToken').textContent = token || '无';
            document.getElementById('userInfo').style.display = 'block';
        }

        function refreshQRCode() {
            stopStatusCheck();
            generateQRCode();
        }

        // 模拟扫码
        async function simulateScan() {
            if (!currentTicket) {
                alert('请先生成二维码');
                return;
            }

            try {
                const response = await fetch('/api/v1/wechat/simulate-scan', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        ticket: currentTicket,
                        action: 'scan'
                    })
                });

                const result = await response.json();
                if (result.code === 200) {
                    updateStatus('scanned', '已扫码，请在微信中确认登录');
                } else {
                    alert('模拟扫码失败：' + result.message);
                }
            } catch (error) {
                console.error('模拟扫码失败:', error);
                alert('模拟扫码失败');
            }
        }

        // 模拟确认登录
        async function simulateConfirm() {
            if (!currentTicket) {
                alert('请先生成二维码');
                return;
            }

            try {
                const response = await fetch('/api/v1/wechat/simulate-scan', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        ticket: currentTicket,
                        action: 'confirm'
                    })
                });

                const result = await response.json();
                if (result.code === 200) {
                    updateStatus('success', '登录成功！');
                    showUserInfo(result.data.user, result.data.token);
                    stopStatusCheck();
                } else {
                    alert('模拟确认登录失败：' + result.message);
                }
            } catch (error) {
                console.error('模拟确认登录失败:', error);
                alert('模拟确认登录失败');
            }
        }

        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', () => {
            stopStatusCheck();
        });
    </script>
</body>
</html>
