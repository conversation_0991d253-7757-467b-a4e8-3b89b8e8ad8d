<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录 - 知识付费平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 400px;
            position: relative;
            overflow: hidden;
        }
        
        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }
        
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo h1 {
            color: #333;
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .logo p {
            color: #666;
            font-size: 14px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
            font-size: 14px;
        }
        
        .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
            background-color: #f8f9fa;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            background-color: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        
        .btn:active {
            transform: translateY(0);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .alert {
            padding: 12px 16px;
            border-radius: 6px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .remember-me {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .remember-me input[type="checkbox"] {
            margin-right: 8px;
        }
        
        .remember-me label {
            font-size: 14px;
            color: #666;
            cursor: pointer;
        }
        
        .links {
            text-align: center;
            margin-top: 20px;
        }
        
        .links a {
            color: #667eea;
            text-decoration: none;
            font-size: 14px;
            margin: 0 10px;
        }
        
        .links a:hover {
            text-decoration: underline;
        }
        
        .loading {
            display: none;
            text-align: center;
            margin-top: 10px;
        }
        
        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .demo-info {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 20px;
            font-size: 13px;
        }
        
        .demo-info strong {
            display: block;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <h1>管理后台</h1>
            <p>知识付费平台管理系统</p>
        </div>
        
        <div class="demo-info">
            <strong>演示账号：</strong>
            用户名: admin<br>
            密码: admin123<br>
            <small>（这是演示环境，实际部署时请修改默认密码）</small>
        </div>
        
        <div id="alertContainer"></div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" class="form-control" required value="admin">
            </div>
            
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" class="form-control" required value="admin123">
            </div>
            
            <div class="remember-me">
                <input type="checkbox" id="rememberMe">
                <label for="rememberMe">记住登录状态</label>
            </div>
            
            <button type="submit" class="btn" id="loginBtn">
                登录
            </button>
            
            <div class="loading" id="loading"></div>
        </form>
        
        <div class="links">
            <a href="/wechat-qr-login.html">用户登录</a>
            <a href="/content-management.html">内容管理演示</a>
        </div>
    </div>

    <script>
        const API_BASE = '/api/v1';
        
        // 显示提示信息
        function showAlert(message, type = 'info') {
            const alertContainer = document.getElementById('alertContainer');
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;
            
            alertContainer.innerHTML = '';
            alertContainer.appendChild(alertDiv);
            
            // 3秒后自动隐藏
            setTimeout(() => {
                alertDiv.remove();
            }, 3000);
        }
        
        // 设置加载状态
        function setLoading(loading) {
            const loginBtn = document.getElementById('loginBtn');
            const loadingDiv = document.getElementById('loading');
            
            if (loading) {
                loginBtn.disabled = true;
                loginBtn.textContent = '登录中...';
                loadingDiv.style.display = 'block';
            } else {
                loginBtn.disabled = false;
                loginBtn.textContent = '登录';
                loadingDiv.style.display = 'none';
            }
        }
        
        // 处理登录表单提交
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('rememberMe').checked;
            
            if (!username || !password) {
                showAlert('请输入用户名和密码', 'error');
                return;
            }
            
            setLoading(true);
            
            try {
                // 模拟登录请求
                const response = await fetch(`${API_BASE}/admin/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username,
                        password,
                        rememberMe
                    })
                });
                
                const result = await response.json();
                
                if (result.code === 200) {
                    // 登录成功
                    showAlert('登录成功！正在跳转...', 'success');
                    
                    // 保存令牌
                    const token = result.data.token;
                    if (rememberMe) {
                        localStorage.setItem('adminToken', token);
                    } else {
                        sessionStorage.setItem('adminToken', token);
                    }
                    
                    // 保存管理员信息
                    localStorage.setItem('adminInfo', JSON.stringify(result.data.admin));
                    
                    // 延迟跳转到管理后台
                    setTimeout(() => {
                        window.location.href = '/admin-dashboard.html';
                    }, 1000);
                    
                } else {
                    showAlert(result.message || '登录失败', 'error');
                }
                
            } catch (error) {
                console.error('登录请求失败:', error);
                
                // 演示模式：模拟登录成功
                if (username === 'admin' && password === 'admin123') {
                    showAlert('演示模式登录成功！正在跳转...', 'success');
                    
                    // 模拟令牌和管理员信息
                    const mockToken = 'demo_admin_token_' + Date.now();
                    const mockAdmin = {
                        id: 1,
                        username: 'admin',
                        nickname: '系统管理员',
                        email: '<EMAIL>',
                        role: {
                            id: 1,
                            name: '超级管理员',
                            permissions: ['*']
                        }
                    };
                    
                    if (rememberMe) {
                        localStorage.setItem('adminToken', mockToken);
                    } else {
                        sessionStorage.setItem('adminToken', mockToken);
                    }
                    
                    localStorage.setItem('adminInfo', JSON.stringify(mockAdmin));
                    
                    setTimeout(() => {
                        window.location.href = '/admin-dashboard.html';
                    }, 1000);
                } else {
                    showAlert('用户名或密码错误', 'error');
                }
            }
            
            setLoading(false);
        });
        
        // 检查是否已经登录
        function checkLoginStatus() {
            const token = localStorage.getItem('adminToken') || sessionStorage.getItem('adminToken');
            if (token) {
                showAlert('检测到已登录状态，正在跳转...', 'info');
                setTimeout(() => {
                    window.location.href = '/admin-dashboard.html';
                }, 1000);
            }
        }
        
        // 页面加载时检查登录状态
        document.addEventListener('DOMContentLoaded', () => {
            checkLoginStatus();
            
            // 显示欢迎信息
            setTimeout(() => {
                showAlert('欢迎使用知识付费平台管理系统', 'info');
            }, 500);
        });
        
        // 回车键登录
        document.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                document.getElementById('loginForm').dispatchEvent(new Event('submit'));
            }
        });
    </script>
</body>
</html>
