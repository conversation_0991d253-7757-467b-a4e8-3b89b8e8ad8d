<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>积分配置与卡密管理 - 知识付费平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0 20px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 20px;
            font-weight: 600;
        }
        
        .header-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .btn {
            background-color: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
            transition: background-color 0.3s;
        }
        
        .btn:hover {
            background-color: #5a6fd8;
        }
        
        .btn-success {
            background-color: #28a745;
        }
        
        .btn-success:hover {
            background-color: #218838;
        }
        
        .btn-danger {
            background-color: #dc3545;
        }
        
        .btn-danger:hover {
            background-color: #c82333;
        }
        
        .btn-secondary {
            background-color: #6c757d;
        }
        
        .btn-secondary:hover {
            background-color: #545b62;
        }
        
        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }
        
        .tabs {
            display: flex;
            background: white;
            border-radius: 8px 8px 0 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .tab {
            flex: 1;
            padding: 15px 20px;
            text-align: center;
            cursor: pointer;
            background: #f8f9fa;
            border-right: 1px solid #dee2e6;
            transition: all 0.3s;
        }
        
        .tab:last-child {
            border-right: none;
        }
        
        .tab.active {
            background: white;
            color: #667eea;
            font-weight: 600;
        }
        
        .tab:hover:not(.active) {
            background: #e9ecef;
        }
        
        .content-area {
            background: white;
            border-radius: 0 0 8px 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            min-height: 600px;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        
        .form-control {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.25);
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        
        .table tr:hover {
            background-color: #f8f9fa;
        }
        
        .badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .badge-success {
            background-color: #d4edda;
            color: #155724;
        }
        
        .badge-warning {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .badge-danger {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .badge-secondary {
            background-color: #e2e3e5;
            color: #383d41;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            border-left: 4px solid #667eea;
        }
        
        .stat-card h3 {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
            text-transform: uppercase;
        }
        
        .stat-card .number {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        
        .alert {
            padding: 12px 16px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: auto;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 0 15px;
            }
            
            .tabs {
                flex-direction: column;
            }
            
            .tab {
                border-right: none;
                border-bottom: 1px solid #dee2e6;
            }
            
            .tab:last-child {
                border-bottom: none;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>积分配置与卡密管理</h1>
        <div class="header-right">
            <a href="/admin-dashboard.html" class="btn btn-secondary">返回后台</a>
        </div>
    </div>
    
    <div class="container">
        <div class="tabs">
            <div class="tab active" onclick="switchTab('points')">积分配置</div>
            <div class="tab" onclick="switchTab('cards')">卡密管理</div>
        </div>
        
        <div class="content-area">
            <div id="alertContainer"></div>
            
            <!-- 积分配置 -->
            <div id="pointsTab" class="tab-content active">
                <h2>积分获取规则配置</h2>
                <p style="color: #666; margin-bottom: 20px;">配置用户通过各种行为获得的积分奖励</p>
                
                <div style="margin-bottom: 20px;">
                    <button class="btn btn-success" onclick="savePointConfigs()">保存配置</button>
                    <button class="btn btn-secondary" onclick="resetPointConfigs()">重置为默认</button>
                </div>
                
                <div id="pointConfigsContainer">
                    <div class="loading">正在加载积分配置...</div>
                </div>
            </div>
            
            <!-- 卡密管理 -->
            <div id="cardsTab" class="tab-content">
                <h2>卡密管理</h2>
                <p style="color: #666; margin-bottom: 20px;">管理系统中的各种卡密，包括积分卡、内容解锁卡等</p>
                
                <!-- 卡密统计 -->
                <div id="cardStatsContainer">
                    <div class="loading">正在加载统计数据...</div>
                </div>
                
                <!-- 生成卡密 -->
                <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                    <h3>批量生成卡密</h3>
                    <form id="generateCardForm" style="margin-top: 15px;">
                        <div class="form-row">
                            <div class="form-group">
                                <label>生成数量</label>
                                <input type="number" class="form-control" id="cardCount" min="1" max="100" value="10" required>
                            </div>
                            <div class="form-group">
                                <label>卡密类型</label>
                                <select class="form-control" id="cardType" required>
                                    <option value="1">积分卡</option>
                                    <option value="2">内容解锁卡</option>
                                    <option value="3">特殊卡</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>卡密价值</label>
                                <input type="number" class="form-control" id="cardValue" min="0" value="100">
                            </div>
                        </div>
                        <div class="form-group">
                            <label>描述</label>
                            <input type="text" class="form-control" id="cardDescription" placeholder="卡密描述">
                        </div>
                        <button type="submit" class="btn btn-success">生成卡密</button>
                    </form>
                </div>
                
                <!-- 卡密列表 -->
                <div style="margin-top: 30px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <h3>卡密列表</h3>
                        <button class="btn" onclick="loadCardCodes()">刷新列表</button>
                    </div>
                    
                    <div id="cardCodesContainer">
                        <div class="loading">正在加载卡密列表...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = '/api/v1';
        
        // 获取管理员令牌
        function getAdminToken() {
            return localStorage.getItem('adminToken') || sessionStorage.getItem('adminToken');
        }
        
        // 检查登录状态
        function checkAuth() {
            const token = getAdminToken();
            if (!token) {
                window.location.href = '/admin-login.html';
                return false;
            }
            return true;
        }
        
        // 显示提示信息
        function showAlert(message, type = 'info') {
            const alertContainer = document.getElementById('alertContainer');
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;
            
            alertContainer.innerHTML = '';
            alertContainer.appendChild(alertDiv);
            
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }
        
        // 切换标签页
        function switchTab(tabName) {
            // 移除所有标签的激活状态
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 激活选中的标签和内容
            event.target.classList.add('active');
            document.getElementById(tabName + 'Tab').classList.add('active');
            
            // 加载相应内容
            if (tabName === 'points') {
                loadPointConfigs();
            } else if (tabName === 'cards') {
                loadCardStats();
                loadCardCodes();
            }
        }
        
        // 加载积分配置
        async function loadPointConfigs() {
            try {
                const response = await fetch(`${API_BASE}/admin/point-configs`, {
                    headers: {
                        'Authorization': `Bearer ${getAdminToken()}`
                    }
                });
                
                const result = await response.json();
                
                if (result.code === 200) {
                    displayPointConfigs(result.data);
                } else {
                    showAlert(`加载失败：${result.message}`, 'error');
                }
            } catch (error) {
                showAlert(`加载失败：${error.message}`, 'error');
            }
        }
        
        // 显示积分配置
        function displayPointConfigs(configs) {
            const container = document.getElementById('pointConfigsContainer');
            
            container.innerHTML = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>行为类型</th>
                            <th>描述</th>
                            <th>积分奖励</th>
                            <th>是否启用</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${configs.map(config => `
                            <tr>
                                <td>${getActionTypeText(config.type)}</td>
                                <td>
                                    <input type="text" class="form-control" 
                                           value="${config.description}" 
                                           onchange="updateConfigField('${config.type}', 'description', this.value)">
                                </td>
                                <td>
                                    <input type="number" class="form-control" 
                                           value="${config.points}" min="0"
                                           onchange="updateConfigField('${config.type}', 'points', parseInt(this.value))">
                                </td>
                                <td>
                                    <div class="checkbox-group">
                                        <input type="checkbox" ${config.isEnabled ? 'checked' : ''}
                                               onchange="updateConfigField('${config.type}', 'isEnabled', this.checked)">
                                        <span>${config.isEnabled ? '启用' : '禁用'}</span>
                                    </div>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
        }
        
        // 获取行为类型文本
        function getActionTypeText(type) {
            const types = {
                'daily_sign': '每日签到',
                'watch_ad': '观看广告',
                'invite_user': '邀请用户',
                'first_share': '首次分享',
                'complete_profile': '完善资料'
            };
            return types[type] || type;
        }
        
        // 更新配置字段
        function updateConfigField(type, field, value) {
            // 这里可以实现实时更新，或者等待用户点击保存按钮
            console.log(`更新配置 ${type} 的 ${field} 为 ${value}`);
        }
        
        // 保存积分配置
        async function savePointConfigs() {
            try {
                // 收集所有配置数据
                const configs = [];
                const rows = document.querySelectorAll('#pointConfigsContainer tbody tr');
                
                rows.forEach(row => {
                    const cells = row.querySelectorAll('td');
                    const typeText = cells[0].textContent.trim();
                    const description = cells[1].querySelector('input').value;
                    const points = parseInt(cells[2].querySelector('input').value);
                    const isEnabled = cells[3].querySelector('input').checked;
                    
                    // 根据文本找到对应的type
                    const typeMap = {
                        '每日签到': 'daily_sign',
                        '观看广告': 'watch_ad',
                        '邀请用户': 'invite_user',
                        '首次分享': 'first_share',
                        '完善资料': 'complete_profile'
                    };
                    
                    const type = typeMap[typeText];
                    if (type) {
                        configs.push({ type, description, points, isEnabled });
                    }
                });
                
                const response = await fetch(`${API_BASE}/admin/point-configs`, {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${getAdminToken()}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ configs })
                });
                
                const result = await response.json();
                
                if (result.code === 200) {
                    showAlert('积分配置保存成功！', 'success');
                } else {
                    showAlert(`保存失败：${result.message}`, 'error');
                }
            } catch (error) {
                showAlert(`保存失败：${error.message}`, 'error');
            }
        }
        
        // 重置积分配置
        async function resetPointConfigs() {
            if (!confirm('确定要重置为默认配置吗？这将覆盖当前所有设置。')) {
                return;
            }
            
            // 这里可以调用重置API
            showAlert('重置功能开发中...', 'info');
        }
        
        // 加载卡密统计
        async function loadCardStats() {
            try {
                const response = await fetch(`${API_BASE}/admin/card-codes/stats`, {
                    headers: {
                        'Authorization': `Bearer ${getAdminToken()}`
                    }
                });
                
                const result = await response.json();
                
                if (result.code === 200) {
                    displayCardStats(result.data);
                } else {
                    showAlert(`加载统计失败：${result.message}`, 'error');
                }
            } catch (error) {
                showAlert(`加载统计失败：${error.message}`, 'error');
            }
        }
        
        // 显示卡密统计
        function displayCardStats(stats) {
            const container = document.getElementById('cardStatsContainer');
            
            container.innerHTML = `
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>总卡密数</h3>
                        <div class="number">${stats.total}</div>
                    </div>
                    <div class="stat-card">
                        <h3>未使用</h3>
                        <div class="number">${stats.byStatus.unused}</div>
                    </div>
                    <div class="stat-card">
                        <h3>已使用</h3>
                        <div class="number">${stats.byStatus.used}</div>
                    </div>
                    <div class="stat-card">
                        <h3>已过期</h3>
                        <div class="number">${stats.byStatus.expired}</div>
                    </div>
                    <div class="stat-card">
                        <h3>积分卡</h3>
                        <div class="number">${stats.byType.pointCards}</div>
                    </div>
                    <div class="stat-card">
                        <h3>内容解锁卡</h3>
                        <div class="number">${stats.byType.contentCards}</div>
                    </div>
                </div>
            `;
        }
        
        // 生成卡密表单提交
        document.getElementById('generateCardForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = {
                count: parseInt(document.getElementById('cardCount').value),
                type: parseInt(document.getElementById('cardType').value),
                value: parseInt(document.getElementById('cardValue').value),
                description: document.getElementById('cardDescription').value
            };
            
            try {
                const response = await fetch(`${API_BASE}/admin/card-codes/batch-generate`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${getAdminToken()}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
                
                const result = await response.json();
                
                if (result.code === 200) {
                    showAlert(`成功生成 ${result.data.count} 个卡密！`, 'success');
                    loadCardStats();
                    loadCardCodes();
                    document.getElementById('generateCardForm').reset();
                } else {
                    showAlert(`生成失败：${result.message}`, 'error');
                }
            } catch (error) {
                showAlert(`生成失败：${error.message}`, 'error');
            }
        });
        
        // 加载卡密列表
        async function loadCardCodes() {
            try {
                const response = await fetch(`${API_BASE}/admin/card-codes`, {
                    headers: {
                        'Authorization': `Bearer ${getAdminToken()}`
                    }
                });
                
                const result = await response.json();
                
                if (result.code === 200) {
                    displayCardCodes(result.data.cardCodes);
                } else {
                    showAlert(`加载失败：${result.message}`, 'error');
                }
            } catch (error) {
                showAlert(`加载失败：${error.message}`, 'error');
            }
        }
        
        // 显示卡密列表
        function displayCardCodes(cardCodes) {
            const container = document.getElementById('cardCodesContainer');
            
            if (cardCodes.length === 0) {
                container.innerHTML = '<div style="text-align: center; padding: 40px; color: #666;">暂无卡密</div>';
                return;
            }
            
            container.innerHTML = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>卡密代码</th>
                            <th>类型</th>
                            <th>价值</th>
                            <th>状态</th>
                            <th>描述</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${cardCodes.map(card => `
                            <tr>
                                <td><code>${card.code}</code></td>
                                <td>${getCardTypeText(card.type)}</td>
                                <td>${card.value}</td>
                                <td>
                                    <span class="badge ${getStatusBadgeClass(card.status)}">
                                        ${getStatusText(card.status)}
                                    </span>
                                </td>
                                <td>${card.description || '-'}</td>
                                <td>${new Date(card.createdAt).toLocaleDateString()}</td>
                                <td>
                                    ${card.status === 1 ? `
                                        <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;" 
                                                onclick="deleteCardCode(${card.id})">删除</button>
                                    ` : '-'}
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
        }
        
        // 获取卡密类型文本
        function getCardTypeText(type) {
            const types = { 1: '积分卡', 2: '内容解锁卡', 3: '特殊卡' };
            return types[type] || '未知';
        }
        
        // 获取状态文本
        function getStatusText(status) {
            const statuses = { 1: '未使用', 2: '已使用', 3: '已过期' };
            return statuses[status] || '未知';
        }
        
        // 获取状态徽章样式
        function getStatusBadgeClass(status) {
            const classes = { 1: 'badge-success', 2: 'badge-secondary', 3: 'badge-danger' };
            return classes[status] || 'badge-secondary';
        }
        
        // 删除卡密
        async function deleteCardCode(cardId) {
            if (!confirm('确定要删除这个卡密吗？')) {
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/admin/card-codes/${cardId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${getAdminToken()}`
                    }
                });
                
                const result = await response.json();
                
                if (result.code === 200) {
                    showAlert('删除成功！', 'success');
                    loadCardStats();
                    loadCardCodes();
                } else {
                    showAlert(`删除失败：${result.message}`, 'error');
                }
            } catch (error) {
                showAlert(`删除失败：${error.message}`, 'error');
            }
        }
        
        // 页面初始化
        document.addEventListener('DOMContentLoaded', () => {
            if (!checkAuth()) return;
            
            // 默认加载积分配置
            loadPointConfigs();
        });
    </script>
</body>
</html>
