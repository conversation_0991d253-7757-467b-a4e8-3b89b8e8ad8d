<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台 - 知识付费平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0 20px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 20px;
            font-weight: 600;
        }
        
        .header-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .admin-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .admin-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .logout-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        
        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
        }
        
        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }
        
        .stat-card h3 {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .stat-card .number {
            font-size: 32px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .stat-card .change {
            font-size: 12px;
            color: #28a745;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 250px 1fr;
            gap: 20px;
        }
        
        .sidebar {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            height: fit-content;
        }
        
        .sidebar h3 {
            margin-bottom: 15px;
            color: #333;
            font-size: 16px;
        }
        
        .nav-menu {
            list-style: none;
        }
        
        .nav-menu li {
            margin-bottom: 5px;
        }
        
        .nav-menu a {
            display: block;
            padding: 10px 15px;
            color: #666;
            text-decoration: none;
            border-radius: 4px;
            transition: all 0.3s;
        }
        
        .nav-menu a:hover,
        .nav-menu a.active {
            background-color: #667eea;
            color: white;
        }
        
        .content-area {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            min-height: 500px;
        }
        
        .content-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        
        .content-header h2 {
            color: #333;
            font-size: 24px;
        }
        
        .btn {
            background-color: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
            transition: background-color 0.3s;
        }
        
        .btn:hover {
            background-color: #5a6fd8;
        }
        
        .btn-success {
            background-color: #28a745;
        }
        
        .btn-success:hover {
            background-color: #218838;
        }
        
        .btn-danger {
            background-color: #dc3545;
        }
        
        .btn-danger:hover {
            background-color: #c82333;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        
        .table tr:hover {
            background-color: #f8f9fa;
        }
        
        .badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .badge-success {
            background-color: #d4edda;
            color: #155724;
        }
        
        .badge-warning {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .badge-danger {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .empty-state {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .empty-state h3 {
            margin-bottom: 10px;
            color: #999;
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .header {
                padding: 0 15px;
            }
            
            .container {
                padding: 0 15px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>知识付费平台 - 管理后台</h1>
        <div class="header-right">
            <div class="admin-info">
                <div class="admin-avatar" id="adminAvatar">A</div>
                <span id="adminName">管理员</span>
            </div>
            <button class="logout-btn" onclick="logout()">退出登录</button>
        </div>
    </div>
    
    <div class="container">
        <!-- 统计卡片 -->
        <div class="stats-grid">
            <div class="stat-card">
                <h3>总用户数</h3>
                <div class="number" id="totalUsers">-</div>
                <div class="change">+12% 较上月</div>
            </div>
            <div class="stat-card">
                <h3>总内容数</h3>
                <div class="number" id="totalContents">-</div>
                <div class="change">+8% 较上月</div>
            </div>
            <div class="stat-card">
                <h3>今日访问</h3>
                <div class="number" id="todayViews">-</div>
                <div class="change">+15% 较昨日</div>
            </div>
            <div class="stat-card">
                <h3>总收入</h3>
                <div class="number" id="totalRevenue">-</div>
                <div class="change">+25% 较上月</div>
            </div>
        </div>
        
        <!-- 主要内容区域 -->
        <div class="main-content">
            <div class="sidebar">
                <h3>管理菜单</h3>
                <ul class="nav-menu">
                    <li><a href="#" class="active" onclick="showSection('dashboard')">仪表盘</a></li>
                    <li><a href="#" onclick="showSection('users')">用户管理</a></li>
                    <li><a href="#" onclick="showSection('contents')">内容管理</a></li>
                    <li><a href="#" onclick="showSection('categories')">分类管理</a></li>
                    <li><a href="#" onclick="showSection('points')">积分配置</a></li>
                    <li><a href="#" onclick="showSection('cards')">卡密管理</a></li>
                    <li><a href="#" onclick="showSection('stats')">数据统计</a></li>
                    <li><a href="#" onclick="showSection('settings')">系统设置</a></li>
                </ul>
            </div>
            
            <div class="content-area">
                <!-- 仪表盘 -->
                <div id="dashboard-section" class="content-section">
                    <div class="content-header">
                        <h2>仪表盘</h2>
                    </div>
                    <div id="dashboardContent">
                        <p>欢迎使用知识付费平台管理后台！</p>
                        <p>这里是系统概览和快速操作入口。</p>
                        
                        <h3 style="margin: 20px 0 10px 0;">快速操作</h3>
                        <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                            <button class="btn" onclick="showSection('contents')">管理内容</button>
                            <button class="btn btn-success" onclick="showSection('users')">查看用户</button>
                            <button class="btn" onclick="showSection('stats')">查看统计</button>
                        </div>
                        
                        <h3 style="margin: 20px 0 10px 0;">系统状态</h3>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 4px;">
                            <p>✅ 数据库连接正常</p>
                            <p>✅ 缓存服务正常</p>
                            <p>✅ 文件存储正常</p>
                            <p>⚠️ 演示模式运行中</p>
                        </div>
                    </div>
                </div>
                
                <!-- 其他内容区域将通过JavaScript动态加载 -->
                <div id="dynamic-content" style="display: none;">
                    <div class="loading">正在加载...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = '/api/v1';
        let currentSection = 'dashboard';
        
        // 获取管理员令牌
        function getAdminToken() {
            return localStorage.getItem('adminToken') || sessionStorage.getItem('adminToken');
        }
        
        // 检查登录状态
        function checkAuth() {
            const token = getAdminToken();
            if (!token) {
                window.location.href = '/admin-login.html';
                return false;
            }
            return true;
        }
        
        // 退出登录
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                localStorage.removeItem('adminToken');
                sessionStorage.removeItem('adminToken');
                localStorage.removeItem('adminInfo');
                window.location.href = '/admin-login.html';
            }
        }
        
        // 显示不同的管理区域
        function showSection(section) {
            // 更新导航菜单状态
            document.querySelectorAll('.nav-menu a').forEach(a => {
                a.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 隐藏所有内容区域
            document.getElementById('dashboard-section').style.display = 'none';
            document.getElementById('dynamic-content').style.display = 'block';
            
            currentSection = section;
            
            // 根据选择的区域加载相应内容
            loadSectionContent(section);
        }
        
        // 加载区域内容
        async function loadSectionContent(section) {
            const dynamicContent = document.getElementById('dynamic-content');
            
            switch (section) {
                case 'dashboard':
                    document.getElementById('dashboard-section').style.display = 'block';
                    document.getElementById('dynamic-content').style.display = 'none';
                    break;
                    
                case 'users':
                    dynamicContent.innerHTML = `
                        <div class="content-header">
                            <h2>用户管理</h2>
                            <button class="btn btn-success">添加用户</button>
                        </div>
                        <div class="loading">正在加载用户列表...</div>
                    `;
                    await loadUsers();
                    break;
                    
                case 'contents':
                    dynamicContent.innerHTML = `
                        <div class="content-header">
                            <h2>内容管理</h2>
                            <button class="btn btn-success" onclick="window.open('/content-management.html', '_blank')">内容管理工具</button>
                        </div>
                        <div class="loading">正在加载内容列表...</div>
                    `;
                    await loadContents();
                    break;
                    
                case 'categories':
                    dynamicContent.innerHTML = `
                        <div class="content-header">
                            <h2>分类管理</h2>
                            <button class="btn btn-success">添加分类</button>
                        </div>
                        <div class="loading">正在加载分类列表...</div>
                    `;
                    await loadCategories();
                    break;
                    
                case 'stats':
                    dynamicContent.innerHTML = `
                        <div class="content-header">
                            <h2>数据统计</h2>
                        </div>
                        <div class="loading">正在加载统计数据...</div>
                    `;
                    await loadStats();
                    break;
                    
                default:
                    dynamicContent.innerHTML = `
                        <div class="content-header">
                            <h2>${getSectionTitle(section)}</h2>
                        </div>
                        <div class="empty-state">
                            <h3>功能开发中</h3>
                            <p>该功能正在开发中，敬请期待...</p>
                        </div>
                    `;
            }
        }
        
        // 获取区域标题
        function getSectionTitle(section) {
            const titles = {
                'points': '积分配置',
                'cards': '卡密管理',
                'settings': '系统设置'
            };
            return titles[section] || section;
        }
        
        // 加载用户列表
        async function loadUsers() {
            try {
                // 模拟用户数据
                const mockUsers = [
                    { id: 1, nickname: '用户001', phone: '138****1234', points: 500, vipLevel: 1, status: 1, createdAt: '2023-12-01' },
                    { id: 2, nickname: '用户002', phone: '139****5678', points: 200, vipLevel: 0, status: 1, createdAt: '2023-12-02' },
                    { id: 3, nickname: '用户003', phone: '137****9012', points: 1000, vipLevel: 2, status: 2, createdAt: '2023-12-03' }
                ];
                
                const dynamicContent = document.getElementById('dynamic-content');
                dynamicContent.innerHTML = `
                    <div class="content-header">
                        <h2>用户管理</h2>
                        <button class="btn btn-success">添加用户</button>
                    </div>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>昵称</th>
                                <th>手机号</th>
                                <th>积分</th>
                                <th>VIP等级</th>
                                <th>状态</th>
                                <th>注册时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${mockUsers.map(user => `
                                <tr>
                                    <td>${user.id}</td>
                                    <td>${user.nickname}</td>
                                    <td>${user.phone}</td>
                                    <td>${user.points}</td>
                                    <td>${user.vipLevel === 0 ? '普通' : 'VIP' + user.vipLevel}</td>
                                    <td>
                                        <span class="badge ${user.status === 1 ? 'badge-success' : 'badge-danger'}">
                                            ${user.status === 1 ? '正常' : '禁用'}
                                        </span>
                                    </td>
                                    <td>${user.createdAt}</td>
                                    <td>
                                        <button class="btn" style="padding: 5px 10px; font-size: 12px;">编辑</button>
                                        <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;">禁用</button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                `;
            } catch (error) {
                console.error('加载用户列表失败:', error);
            }
        }
        
        // 加载内容列表
        async function loadContents() {
            try {
                const response = await fetch(`${API_BASE}/content`, {
                    headers: {
                        'Authorization': `Bearer ${getAdminToken()}`
                    }
                });
                
                let contents = [];
                if (response.ok) {
                    const result = await response.json();
                    contents = result.data?.contents || [];
                }
                
                const dynamicContent = document.getElementById('dynamic-content');
                dynamicContent.innerHTML = `
                    <div class="content-header">
                        <h2>内容管理</h2>
                        <button class="btn btn-success" onclick="window.open('/content-management.html', '_blank')">内容管理工具</button>
                    </div>
                    ${contents.length > 0 ? `
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>标题</th>
                                    <th>类型</th>
                                    <th>解锁方式</th>
                                    <th>价格</th>
                                    <th>浏览数</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${contents.map(content => `
                                    <tr>
                                        <td>${content.id}</td>
                                        <td>${content.title}</td>
                                        <td>${content.contentTypeText || '文章'}</td>
                                        <td>${content.unlockTypeText || '免费'}</td>
                                        <td>${content.unlockPrice || 0}</td>
                                        <td>${content.viewCount || 0}</td>
                                        <td>
                                            <span class="badge badge-success">正常</span>
                                        </td>
                                        <td>
                                            <button class="btn" style="padding: 5px 10px; font-size: 12px;">编辑</button>
                                            <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;">删除</button>
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    ` : `
                        <div class="empty-state">
                            <h3>暂无内容</h3>
                            <p>还没有创建任何内容，点击上方按钮开始创建。</p>
                        </div>
                    `}
                `;
            } catch (error) {
                console.error('加载内容列表失败:', error);
            }
        }
        
        // 加载分类列表
        async function loadCategories() {
            try {
                const response = await fetch(`${API_BASE}/categories`);
                let categories = [];
                
                if (response.ok) {
                    const result = await response.json();
                    categories = result.data || [];
                }
                
                const dynamicContent = document.getElementById('dynamic-content');
                dynamicContent.innerHTML = `
                    <div class="content-header">
                        <h2>分类管理</h2>
                        <button class="btn btn-success">添加分类</button>
                    </div>
                    ${categories.length > 0 ? `
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>名称</th>
                                    <th>标识</th>
                                    <th>描述</th>
                                    <th>排序</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${categories.map(category => `
                                    <tr>
                                        <td>${category.id}</td>
                                        <td>${category.name}</td>
                                        <td>${category.slug}</td>
                                        <td>${category.description || '-'}</td>
                                        <td>${category.sortOrder || 0}</td>
                                        <td>
                                            <button class="btn" style="padding: 5px 10px; font-size: 12px;">编辑</button>
                                            <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;">删除</button>
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    ` : `
                        <div class="empty-state">
                            <h3>暂无分类</h3>
                            <p>还没有创建任何分类，点击上方按钮开始创建。</p>
                        </div>
                    `}
                `;
            } catch (error) {
                console.error('加载分类列表失败:', error);
            }
        }
        
        // 加载统计数据
        async function loadStats() {
            const dynamicContent = document.getElementById('dynamic-content');
            dynamicContent.innerHTML = `
                <div class="content-header">
                    <h2>数据统计</h2>
                </div>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
                        <h3>用户统计</h3>
                        <p>总用户数: 1,234</p>
                        <p>活跃用户: 856</p>
                        <p>新增用户: 45 (本周)</p>
                    </div>
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
                        <h3>内容统计</h3>
                        <p>总内容数: 567</p>
                        <p>今日浏览: 2,345</p>
                        <p>热门内容: 89</p>
                    </div>
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
                        <h3>收入统计</h3>
                        <p>总收入: ¥12,345</p>
                        <p>本月收入: ¥3,456</p>
                        <p>今日收入: ¥234</p>
                    </div>
                </div>
            `;
        }
        
        // 初始化页面
        function initPage() {
            if (!checkAuth()) return;
            
            // 加载管理员信息
            const adminInfo = JSON.parse(localStorage.getItem('adminInfo') || '{}');
            if (adminInfo.nickname) {
                document.getElementById('adminName').textContent = adminInfo.nickname;
                document.getElementById('adminAvatar').textContent = adminInfo.nickname.charAt(0).toUpperCase();
            }
            
            // 加载统计数据
            loadDashboardStats();
        }
        
        // 加载仪表盘统计数据
        async function loadDashboardStats() {
            // 模拟统计数据
            document.getElementById('totalUsers').textContent = '1,234';
            document.getElementById('totalContents').textContent = '567';
            document.getElementById('todayViews').textContent = '2,345';
            document.getElementById('totalRevenue').textContent = '¥12,345';
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initPage);
    </script>
</body>
</html>
