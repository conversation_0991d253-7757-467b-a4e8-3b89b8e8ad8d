#!/bin/bash

# 知识付费平台快速部署脚本
# 适用于 Ubuntu/CentOS/macOS

set -e

echo "🚀 知识付费平台快速部署脚本"
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查系统类型
detect_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        if [ -f /etc/redhat-release ]; then
            OS="centos"
        elif [ -f /etc/debian_version ]; then
            OS="ubuntu"
        else
            OS="linux"
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
    else
        OS="unknown"
    fi
    log_info "检测到操作系统: $OS"
}

# 检查并安装Node.js
install_nodejs() {
    log_step "检查Node.js安装状态..."
    
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        log_info "Node.js已安装: $NODE_VERSION"
        
        # 检查版本是否满足要求
        MAJOR_VERSION=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
        if [ "$MAJOR_VERSION" -lt 14 ]; then
            log_warn "Node.js版本过低，需要升级到14.x或更高版本"
            install_node_force=true
        else
            install_node_force=false
        fi
    else
        log_warn "Node.js未安装"
        install_node_force=true
    fi
    
    if [ "$install_node_force" = true ]; then
        log_step "安装Node.js..."
        case $OS in
            "ubuntu")
                curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
                sudo apt-get install -y nodejs
                ;;
            "centos")
                curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
                sudo yum install -y nodejs
                ;;
            "macos")
                if command -v brew &> /dev/null; then
                    brew install node
                else
                    log_error "请先安装Homebrew或手动安装Node.js"
                    exit 1
                fi
                ;;
            *)
                log_error "不支持的操作系统，请手动安装Node.js 14.x或更高版本"
                exit 1
                ;;
        esac
        log_info "Node.js安装完成"
    fi
}

# 检查并安装MySQL客户端
install_mysql_client() {
    log_step "检查MySQL客户端..."
    
    if command -v mysql &> /dev/null; then
        log_info "MySQL客户端已安装"
    else
        log_step "安装MySQL客户端..."
        case $OS in
            "ubuntu")
                sudo apt-get update
                sudo apt-get install -y mysql-client
                ;;
            "centos")
                sudo yum install -y mysql
                ;;
            "macos")
                if command -v brew &> /dev/null; then
                    brew install mysql-client
                else
                    log_warn "请手动安装MySQL客户端"
                fi
                ;;
        esac
        log_info "MySQL客户端安装完成"
    fi
}

# 测试数据库连接
test_database_connection() {
    log_step "测试数据库连接..."
    
    DB_HOST="*************"
    DB_PORT="3306"
    DB_USER="root"
    DB_PASS="mysql_jZKFP62"
    DB_NAME="zhis"
    
    if mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASS -e "SELECT 1;" &> /dev/null; then
        log_info "数据库连接成功"
        
        # 检查数据库是否存在
        if mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASS -e "USE $DB_NAME;" &> /dev/null; then
            log_info "数据库 $DB_NAME 已存在"
        else
            log_warn "数据库 $DB_NAME 不存在，将创建数据库"
            mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASS -e "CREATE DATABASE IF NOT EXISTS $DB_NAME DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
            log_info "数据库 $DB_NAME 创建成功"
        fi
    else
        log_error "数据库连接失败，请检查网络连接和数据库配置"
        log_error "数据库地址: $DB_HOST:$DB_PORT"
        log_error "用户名: $DB_USER"
        exit 1
    fi
}

# 初始化数据库
init_database() {
    log_step "初始化数据库..."
    
    if [ -f "database/setup.sql" ]; then
        mysql -h ************* -P 3306 -u root -pmysql_jZKFP62 < database/setup.sql
        log_info "数据库初始化完成"
    else
        log_error "数据库初始化脚本不存在: database/setup.sql"
        exit 1
    fi
}

# 安装项目依赖
install_dependencies() {
    log_step "安装项目依赖..."
    
    if [ -f "package.json" ]; then
        # 检查npm是否可用
        if command -v npm &> /dev/null; then
            npm install
            log_info "依赖安装完成"
        else
            log_error "npm未找到，请检查Node.js安装"
            exit 1
        fi
    else
        log_error "package.json文件不存在"
        exit 1
    fi
}

# 创建环境变量文件
create_env_file() {
    log_step "创建环境变量文件..."
    
    if [ ! -f ".env" ]; then
        cat > .env << EOF
# 数据库配置
DB_HOST=*************
DB_PORT=3306
DB_NAME=zhis
DB_USER=root
DB_PASSWORD=mysql_jZKFP62

# 服务器配置
PORT=3000
NODE_ENV=production

# JWT配置
JWT_SECRET=$(openssl rand -base64 32)
JWT_EXPIRES_IN=7d

# 微信配置
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log
EOF
        log_info "环境变量文件创建完成"
    else
        log_info "环境变量文件已存在"
    fi
}

# 创建日志目录
create_log_directory() {
    log_step "创建日志目录..."
    
    if [ ! -d "logs" ]; then
        mkdir -p logs
        log_info "日志目录创建完成"
    else
        log_info "日志目录已存在"
    fi
}

# 运行测试
run_tests() {
    log_step "运行系统测试..."
    
    # 启动测试服务器（后台运行）
    node test-server.js &
    SERVER_PID=$!
    
    # 等待服务器启动
    sleep 5
    
    # 运行测试
    if node test-complete-system.js; then
        log_info "系统测试通过"
    else
        log_error "系统测试失败"
        kill $SERVER_PID 2>/dev/null || true
        exit 1
    fi
    
    # 停止测试服务器
    kill $SERVER_PID 2>/dev/null || true
}

# 安装PM2（生产环境）
install_pm2() {
    log_step "安装PM2进程管理器..."
    
    if command -v pm2 &> /dev/null; then
        log_info "PM2已安装"
    else
        npm install -g pm2
        log_info "PM2安装完成"
    fi
}

# 创建PM2配置文件
create_pm2_config() {
    log_step "创建PM2配置文件..."
    
    cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'zhis-platform',
    script: 'test-server.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
EOF
    log_info "PM2配置文件创建完成"
}

# 启动服务
start_service() {
    log_step "启动服务..."
    
    if command -v pm2 &> /dev/null; then
        pm2 start ecosystem.config.js
        pm2 save
        pm2 startup
        log_info "服务启动完成（PM2管理）"
    else
        log_info "启动开发服务器..."
        node test-server.js &
        echo $! > server.pid
        log_info "服务启动完成（开发模式）"
    fi
}

# 显示部署结果
show_result() {
    echo ""
    echo "🎉 部署完成！"
    echo "=================================="
    echo ""
    echo "📊 系统信息:"
    echo "  - 服务地址: http://localhost:3000"
    echo "  - 数据库: *************:3306/zhis"
    echo "  - 日志目录: ./logs/"
    echo ""
    echo "🌐 访问地址:"
    echo "  - 微信扫码登录: http://localhost:3000/wechat-qr-login.html"
    echo "  - 内容管理演示: http://localhost:3000/content-management.html"
    echo "  - 管理员登录: http://localhost:3000/admin-login.html"
    echo "  - 管理后台: http://localhost:3000/admin-dashboard.html"
    echo "  - 积分卡密管理: http://localhost:3000/admin-points-cards.html"
    echo ""
    echo "🔑 演示账号:"
    echo "  - 用户名: admin"
    echo "  - 密码: admin123"
    echo ""
    echo "🔧 管理命令:"
    if command -v pm2 &> /dev/null; then
        echo "  - 查看状态: pm2 status"
        echo "  - 查看日志: pm2 logs"
        echo "  - 重启服务: pm2 restart zhis-platform"
        echo "  - 停止服务: pm2 stop zhis-platform"
    else
        echo "  - 停止服务: kill \$(cat server.pid)"
        echo "  - 查看日志: tail -f logs/app.log"
    fi
    echo ""
    echo "📖 更多信息请查看 README.md"
}

# 主函数
main() {
    echo "开始部署知识付费平台..."
    echo ""
    
    # 检测操作系统
    detect_os
    
    # 安装依赖
    install_nodejs
    install_mysql_client
    
    # 数据库相关
    test_database_connection
    init_database
    
    # 项目配置
    install_dependencies
    create_env_file
    create_log_directory
    
    # 测试系统
    run_tests
    
    # 生产环境配置
    install_pm2
    create_pm2_config
    
    # 启动服务
    start_service
    
    # 显示结果
    show_result
}

# 错误处理
trap 'log_error "部署过程中发生错误，请检查日志"; exit 1' ERR

# 运行主函数
main "$@"
