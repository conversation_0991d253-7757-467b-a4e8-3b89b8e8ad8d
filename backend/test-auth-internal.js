const { User, Admin, AdminRole } = require('./src/models');
const { generateToken, verifyToken } = require('./src/middleware/auth');
const authController = require('./src/controllers/authController');
const logger = require('./src/utils/logger');
const bcrypt = require('bcryptjs');

async function testAuthSystem() {
  try {
    logger.info('开始测试认证系统...');

    // 测试1: 创建测试用户
    logger.info('测试1: 创建测试用户');
    const testUser = await User.create({
      phone: '13800138000',
      nickname: '测试用户',
      points: 100,
      inviteCode: 'TEST001'
    }, {
      fields: ['phone', 'nickname', 'points', 'inviteCode']
    });
    logger.info(`创建测试用户成功: ${testUser.id}`);

    // 测试2: 生成和验证JWT令牌
    logger.info('测试2: JWT令牌生成和验证');
    const userToken = authController.generateToken(testUser);
    logger.info(`生成用户令牌: ${userToken.substring(0, 50)}...`);
    
    const decodedUser = verifyToken(userToken);
    logger.info(`验证用户令牌成功: userId=${decodedUser.userId}, type=${decodedUser.type}`);

    // 测试3: 创建测试管理员
    logger.info('测试3: 创建测试管理员');
    
    // 先查找超级管理员角色
    const superAdminRole = await AdminRole.findOne({
      where: { name: '超级管理员' }
    });
    
    if (!superAdminRole) {
      throw new Error('超级管理员角色不存在');
    }

    const hashedPassword = await bcrypt.hash('test123', 10);
    const testAdmin = await Admin.create({
      username: 'testadmin',
      password: hashedPassword,
      nickname: '测试管理员',
      roleId: superAdminRole.id
    });
    logger.info(`创建测试管理员成功: ${testAdmin.id}`);

    // 测试4: 管理员令牌生成和验证
    logger.info('测试4: 管理员JWT令牌生成和验证');
    const adminToken = authController.generateAdminToken(testAdmin);
    logger.info(`生成管理员令牌: ${adminToken.substring(0, 50)}...`);
    
    const decodedAdmin = verifyToken(adminToken);
    logger.info(`验证管理员令牌成功: adminId=${decodedAdmin.adminId}, type=${decodedAdmin.type}`);

    // 测试5: 刷新令牌
    logger.info('测试5: 刷新令牌生成和验证');
    const refreshToken = authController.generateRefreshToken(testUser, 'user');
    logger.info(`生成刷新令牌: ${refreshToken.substring(0, 50)}...`);
    
    const decodedRefresh = verifyToken(refreshToken);
    logger.info(`验证刷新令牌成功: userId=${decodedRefresh.userId}, type=${decodedRefresh.type}`);

    // 测试6: 用户查找方法
    logger.info('测试6: 用户查找方法');
    
    const foundByMobile = await User.findByMobile('13800138000');
    logger.info(`通过手机号查找用户: ${foundByMobile ? foundByMobile.id : '未找到'}`);
    
    const foundByInviteCode = await User.findByInviteCode(testUser.inviteCode);
    logger.info(`通过邀请码查找用户: ${foundByInviteCode ? foundByInviteCode.id : '未找到'}`);

    // 测试7: 用户积分操作
    logger.info('测试7: 用户积分操作');
    const initialPoints = testUser.points;
    await testUser.addPoints(50, '测试奖励');
    await testUser.reload();
    logger.info(`积分增加: ${initialPoints} -> ${testUser.points}`);
    
    await testUser.deductPoints(20, '测试消费');
    await testUser.reload();
    logger.info(`积分扣除: ${testUser.points + 20} -> ${testUser.points}`);

    // 测试8: VIP状态检查
    logger.info('测试8: VIP状态检查');
    const isVip = testUser.isVip();
    const remainingDays = testUser.getVipRemainingDays();
    logger.info(`VIP状态: ${isVip}, 剩余天数: ${remainingDays}`);

    // 清理测试数据
    logger.info('清理测试数据...');
    await testUser.destroy();
    await testAdmin.destroy();
    logger.info('测试数据清理完成');

    logger.info('✅ 认证系统测试全部通过！');

  } catch (error) {
    logger.error('❌ 认证系统测试失败:', error);
    throw error;
  }
}

// 如果直接运行此文件
if (require.main === module) {
  testAuthSystem().then(() => {
    logger.info('认证系统测试完成');
    process.exit(0);
  }).catch((error) => {
    logger.error('认证系统测试失败:', error);
    process.exit(1);
  });
}

module.exports = {
  testAuthSystem
};
