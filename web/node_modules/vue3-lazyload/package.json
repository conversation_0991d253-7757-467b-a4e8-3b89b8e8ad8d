{"name": "vue3-lazyload", "version": "0.3.8", "packageManager": "pnpm@6.32.3", "description": "A Vue3.x image lazyload plugin", "license": "MIT", "author": "木荣 <<EMAIL>>", "repository": {"type": "git", "url": "git+https://github.com/murongg/vue3-lazyload.git"}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "unpkg": "dist/index.min.js", "exports": {".": {"require": "./dist/index.cjs", "import": "./dist/index.mjs", "types": "./dist/index.d.ts"}}, "typesVersions": {"*": {"*": ["./dist/*", "./dist/index.d.ts"]}}, "files": ["dist"], "sideEffects": false, "scripts": {"build": "rimraf dist && unbuild && npm run rollup:build", "rollup:build": "node rollup.config.js", "dev": "unbuild --stub", "lint": "eslint .", "lint:fix": "eslint . --fix", "demo:dev": "vite", "demo:build": "vite build", "prepublishOnly": "nr build", "release": "bumpp --commit --push --tag && pnpm publish", "start": "esno src/index.ts", "test": "vitest", "typecheck": "tsc --noEmit", "prepare": "husky install"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^2.0.0 || >=3.0.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "dependencies": {"vue-demi": "^0.12.5"}, "devDependencies": {"@babel/core": "^7.18.6", "@babel/types": "^7.17.0", "@murongg/eslint-config": "^0.1.0", "@rollup/plugin-babel": "^5.3.1", "@rollup/plugin-commonjs": "^22.0.1", "@rollup/plugin-node-resolve": "^13.3.0", "@rollup/plugin-typescript": "^8.3.3", "@types/node": "^14.11.8", "@vitejs/plugin-vue": "^2.3.2", "@vue/eslint-config-typescript": "^7.0.0", "@vue/test-utils": "^2.0.0-beta.6", "bumpp": "^7.1.1", "eslint": "^8.13.0", "esno": "^0.14.1", "happy-dom": "^3.1.1", "husky": "^8.0.1", "lint-staged": "^13.0.0", "pnpm": "^6.32.6", "prettier": "^2.1.2", "rimraf": "^3.0.2", "rollup": "^2.75.7", "rollup-plugin-uglify": "^6.0.4", "typescript": "^4.6.3", "unbuild": "^0.7.2", "vite": "^2.9.1", "vitest": "^0.9.3", "vue": "^3.2.33"}, "lint-staged": {"*": "pnpm run lint:fix"}, "typings": "types/index.d.ts", "bugs": {"url": "https://github.com/murongg/vue3-lazyload/issues"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "homepage": "https://github.com/murongg/vue3-lazyload#readme"}