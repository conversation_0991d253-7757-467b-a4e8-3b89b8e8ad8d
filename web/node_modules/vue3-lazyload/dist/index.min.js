/*!
 * Vue3-Lazyload.js v0.3.8
 * A Vue3.x image lazyload plugin
 * (c) 2023 MuRong <<EMAIL>>
 * Released under the MIT License.
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("vue")):"function"==typeof define&&define.amd?define(["exports","vue"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).VueLazyload={},e.vue)}(this,function(e,n){"use strict";var s,t;(t=s=s||{}).LOADING="loading",t.LOADED="loaded",t.ERROR="error";const r="undefined"!=typeof window&&null!==window,l=function(){if(r&&"IntersectionObserver"in window&&"IntersectionObserverEntry"in window&&"intersectionRatio"in window.IntersectionObserverEntry.prototype)return"isIntersecting"in window.IntersectionObserverEntry.prototype||Object.defineProperty(window.IntersectionObserverEntry.prototype,"isIntersecting",{get(){return 0<this.intersectionRatio}}),!0;return!1}(),u=Object.prototype.propertyIsEnumerable,d=Object.getOwnPropertySymbols;function g(e){return"function"==typeof e||"[object Object]"===toString.call(e)}function b(e,...t){let r=0;var o,n;for(e=(e=("object"==typeof(o=e)?null===o:"function"!=typeof o)?t[r++]:e)||{};r<t.length;r++)if(g(t[r])){for(const l of Object.keys(t[r]))"__proto__"!==(n=l)&&"constructor"!==n&&"prototype"!==n&&(g(e[l])&&g(t[r][l])?b(e[l],t[r][l]):e[l]=t[r][l]);s=i=void 0;var i=e,s=[t[r]];if(!g(i))throw new TypeError("expected the first argument to be an object");if(0!==s.length&&"function"==typeof Symbol&&"function"==typeof d)for(const a of s)for(const c of d(a))u.call(a,c)&&(i[c]=a[c])}}const a="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",o={rootMargin:"0px",threshold:0},c="data-lazy-timeout-id";class i{constructor(e){this.options={loading:a,error:"",observerOptions:o,log:!0,lifecycle:{},logLevel:"error"},this._images=new WeakMap,this.config(e)}config(e={}){b(this.options,e)}mount(e,t){var r,o,n,i;e&&({src:t,loading:r,error:o,lifecycle:n,delay:i}=this._valueFormatter("string"==typeof t?t:t.value),this._lifecycle(s.LOADING,n,e),e.setAttribute("src",r||a),l||(this.loadImages(e,t,o,n),this._log(()=>{this._logger("Not support IntersectionObserver!")})),this._initIntersectionObserver(e,t,o,n,i))}update(e,t){var r,o,n;e&&(null!=(r=this._realObserver(e))&&r.unobserve(e),{src:r,error:t,lifecycle:o,delay:n}=this._valueFormatter("string"==typeof t?t:t.value),this._initIntersectionObserver(e,r,t,o,n))}unmount(e){var t;e&&(null!=(t=this._realObserver(e))&&t.unobserve(e),this._images.delete(e))}loadImages(e,t,r,o){this._setImageSrc(e,t,r,o)}_setImageSrc(t,r,o,n){"img"===t.tagName.toLowerCase()?(r&&t.getAttribute("src")!==r&&t.setAttribute("src",r),this._listenImageStatus(t,()=>{this._lifecycle(s.LOADED,n,t)},()=>{var e;t.onload=null,this._lifecycle(s.ERROR,n,t),null!=(e=this._realObserver(t))&&e.disconnect(),o&&t.getAttribute("src")!==o&&t.setAttribute("src",o),this._log(()=>{this._logger(`Image failed to load!And failed src was: ${r} `)})})):t.style.backgroundImage=`url('${r}')`}_initIntersectionObserver(t,r,o,n,i){var e=this.options.observerOptions;this._images.set(t,new IntersectionObserver(e=>{Array.prototype.forEach.call(e,e=>{i&&0<i?this._delayedIntersectionCallback(t,e,i,r,o,n):this._intersectionCallback(t,e,r,o,n)})},e)),null!=(e=this._realObserver(t))&&e.observe(t)}_intersectionCallback(e,t,r,o,n){var i;t.isIntersecting&&(null!=(i=this._realObserver(e))&&i.unobserve(t.target),this._setImageSrc(e,r,o,n))}_delayedIntersectionCallback(e,t,r,o,n,i){t.isIntersecting?t.target.hasAttribute(c)||(r=setTimeout(()=>{this._intersectionCallback(e,t,o,n,i),t.target.removeAttribute(c)},r),t.target.setAttribute(c,String(r))):t.target.hasAttribute(c)&&(clearTimeout(Number(t.target.getAttribute(c))),t.target.removeAttribute(c))}_listenImageStatus(e,t,r){e.onload=t,e.onerror=r}_valueFormatter(e){let t=e,r=this.options.loading,o=this.options.error,n=this.options.lifecycle,i=this.options.delay;return g(e)&&(t=e.src,r=e.loading||this.options.loading,o=e.error||this.options.error,n=e.lifecycle||this.options.lifecycle,i=e.delay||this.options.delay),{src:t,loading:r,error:o,lifecycle:n,delay:i}}_log(e){this.options.log&&e()}_lifecycle(e,t,r){switch(e){case s.LOADING:null!=r&&r.setAttribute("lazy",s.LOADING),null!=t&&t.loading&&t.loading(r);break;case s.LOADED:null!=r&&r.setAttribute("lazy",s.LOADED),null!=t&&t.loaded&&t.loaded(r);break;case s.ERROR:null!=r&&r.setAttribute("lazy",s.ERROR),null!=t&&t.error&&t.error(r)}}_realObserver(e){return this._images.get(e)}_logger(e,...t){let r=console.error;switch(this.options.logLevel){case"error":r=console.error;break;case"warn":r=console.warn;break;case"info":r=console.info;break;case"debug":r=console.debug}r(e,t)}}e.default={install(e,t){const r=new i(t);e.config.globalProperties.$Lazyload=r,e.provide("Lazyload",r),e.directive("lazy",{mounted:r.mount.bind(r),updated:r.update.bind(r),unmounted:r.unmount.bind(r)})}},e.useLazyload=function(t,e){const r=n.ref(null),o=new i(e);return n.onMounted(()=>{r.value&&o.mount(r.value,t.value)}),n.onUnmounted(()=>{r.value&&o.unmount(r.value)}),n.watch(t,e=>{t.value&&o.update(r.value,e)}),r},Object.defineProperty(e,"__esModule",{value:!0})});
