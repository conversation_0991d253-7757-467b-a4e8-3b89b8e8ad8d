import { defineStore } from 'pinia'
import { ref } from 'vue'
import api from '@/api'

export const useSystemStore = defineStore('system', () => {
  // 状态
  const config = ref({})
  const categories = ref([])
  const tags = ref([])
  const loading = ref(false)

  // 加载系统配置
  async function loadConfig() {
    try {
      const result = await api.public.getSystemConfig()
      
      if (result.code === 200) {
        config.value = result.data
        
        // 设置页面标题
        if (result.data.site_name) {
          document.title = result.data.site_name
        }
        
        return result.data
      }
    } catch (error) {
      console.error('加载系统配置失败:', error)
    }
  }

  // 加载分类列表
  async function loadCategories() {
    try {
      const result = await api.public.getCategories()
      
      if (result.code === 200) {
        categories.value = result.data
        return result.data
      }
    } catch (error) {
      console.error('加载分类列表失败:', error)
    }
  }

  // 加载标签列表
  async function loadTags(params = {}) {
    try {
      const result = await api.public.getTags(params)
      
      if (result.code === 200) {
        tags.value = result.data
        return result.data
      }
    } catch (error) {
      console.error('加载标签列表失败:', error)
    }
  }

  // 获取首页数据
  async function getHomeData() {
    try {
      loading.value = true
      const result = await api.public.getHomeData()
      
      if (result.code === 200) {
        return result.data
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      console.error('获取首页数据失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取统计信息
  async function getStats() {
    try {
      const result = await api.public.getPublicStats()
      
      if (result.code === 200) {
        return result.data
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      console.error('获取统计信息失败:', error)
      throw error
    }
  }

  return {
    // 状态
    config,
    categories,
    tags,
    loading,
    
    // 方法
    loadConfig,
    loadCategories,
    loadTags,
    getHomeData,
    getStats
  }
})
