import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '@/api'
import { getToken, setToken, removeToken } from '@/utils/auth'
import { ElMessage } from 'element-plus'

export const useUserStore = defineStore('user', () => {
  // 状态
  const userInfo = ref(null)
  const token = ref(getToken())
  const loading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value && !!userInfo.value)
  const isVip = computed(() => {
    if (!userInfo.value) return false
    return userInfo.value.vipLevel > 0 && 
           userInfo.value.vipExpireTime && 
           new Date(userInfo.value.vipExpireTime) > new Date()
  })
  const vipRemainingDays = computed(() => {
    if (!isVip.value) return 0
    const expireTime = new Date(userInfo.value.vipExpireTime)
    const now = new Date()
    const diffTime = expireTime - now
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  })

  // 检查登录状态
  async function checkLoginStatus() {
    if (!token.value) return false
    
    try {
      const result = await api.auth.verify()
      if (result.code === 200) {
        userInfo.value = result.data.user
        return true
      } else {
        logout()
        return false
      }
    } catch (error) {
      console.error('验证登录状态失败:', error)
      logout()
      return false
    }
  }

  // 登录
  async function login(loginData) {
    try {
      loading.value = true
      const result = await api.auth.login(loginData)
      
      if (result.code === 200) {
        token.value = result.data.token.accessToken
        userInfo.value = result.data.user
        setToken(result.data.token.accessToken)
        
        ElMessage.success('登录成功')
        return result.data
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      ElMessage.error(error.message || '登录失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 微信登录
  async function wechatLogin(wechatData) {
    try {
      loading.value = true
      const result = await api.auth.wechatLogin(wechatData)
      
      if (result.code === 200) {
        token.value = result.data.token.accessToken
        userInfo.value = result.data.user
        setToken(result.data.token.accessToken)
        
        ElMessage.success('登录成功')
        return result.data
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      ElMessage.error(error.message || '微信登录失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 注册
  async function register(registerData) {
    try {
      loading.value = true
      const result = await api.auth.register(registerData)
      
      if (result.code === 200) {
        token.value = result.data.token.accessToken
        userInfo.value = result.data.user
        setToken(result.data.token.accessToken)
        
        ElMessage.success('注册成功')
        return result.data
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      ElMessage.error(error.message || '注册失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  // 登出
  async function logout() {
    try {
      if (token.value) {
        await api.auth.logout()
      }
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      token.value = null
      userInfo.value = null
      removeToken()
    }
  }

  // 更新用户信息
  async function updateProfile(profileData) {
    try {
      const result = await api.user.updateProfile(profileData)
      
      if (result.code === 200) {
        userInfo.value = { ...userInfo.value, ...result.data }
        ElMessage.success('更新成功')
        return result.data
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      ElMessage.error(error.message || '更新失败')
      throw error
    }
  }

  // 获取用户详细信息
  async function fetchUserInfo() {
    try {
      const result = await api.user.getProfile()
      
      if (result.code === 200) {
        userInfo.value = result.data
        return result.data
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw error
    }
  }

  // 获取积分信息
  async function fetchPointsInfo() {
    try {
      const result = await api.user.getPoints()
      
      if (result.code === 200) {
        // 更新用户信息中的积分
        if (userInfo.value) {
          userInfo.value.points = result.data.currentPoints
          userInfo.value.totalPoints = result.data.totalPoints
        }
        return result.data
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      console.error('获取积分信息失败:', error)
      throw error
    }
  }

  // 每日签到
  async function dailySign() {
    try {
      const result = await api.point.dailySign()
      
      if (result.code === 200) {
        // 更新用户积分
        if (userInfo.value) {
          userInfo.value.points = result.data.totalPoints
        }
        ElMessage.success(`签到成功，获得${result.data.points}积分`)
        return result.data
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      ElMessage.error(error.message || '签到失败')
      throw error
    }
  }

  // 观看广告
  async function watchAd(adData) {
    try {
      const result = await api.point.watchAd(adData)
      
      if (result.code === 200) {
        // 更新用户积分
        if (userInfo.value) {
          userInfo.value.points = result.data.totalPoints
        }
        ElMessage.success(`观看广告成功，获得${result.data.points}积分`)
        return result.data
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      ElMessage.error(error.message || '观看广告失败')
      throw error
    }
  }

  // 卡密兑换
  async function exchangeCard(cardCode) {
    try {
      const result = await api.point.cardExchange({ cardCode })
      
      if (result.code === 200) {
        // 更新用户信息
        userInfo.value = result.data.user
        ElMessage.success('兑换成功')
        return result.data
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      ElMessage.error(error.message || '兑换失败')
      throw error
    }
  }

  return {
    // 状态
    userInfo,
    token,
    loading,
    
    // 计算属性
    isLoggedIn,
    isVip,
    vipRemainingDays,
    
    // 方法
    checkLoginStatus,
    login,
    wechatLogin,
    register,
    logout,
    updateProfile,
    fetchUserInfo,
    fetchPointsInfo,
    dailySign,
    watchAd,
    exchangeCard
  }
})
