import Cookies from 'js-cookie'

const TOKEN_KEY = 'zhis_token'
const USER_INFO_KEY = 'zhis_user_info'

// Token相关操作
export function getToken() {
  return Cookies.get(TOKEN_KEY) || localStorage.getItem(TOKEN_KEY)
}

export function setToken(token) {
  Cookies.set(TOKEN_KEY, token, { expires: 7 }) // 7天过期
  localStorage.setItem(TOKEN_KEY, token)
}

export function removeToken() {
  Cookies.remove(TOKEN_KEY)
  localStorage.removeItem(TOKEN_KEY)
  localStorage.removeItem(USER_INFO_KEY)
}

// 用户信息相关操作
export function getUserInfo() {
  const userInfo = localStorage.getItem(USER_INFO_KEY)
  return userInfo ? JSON.parse(userInfo) : null
}

export function setUserInfo(userInfo) {
  localStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo))
}

export function removeUserInfo() {
  localStorage.removeItem(USER_INFO_KEY)
}

// 检查是否已登录
export function isLoggedIn() {
  return !!getToken()
}

// 清除所有认证信息
export function clearAuth() {
  removeToken()
  removeUserInfo()
}
