// 检测是否在微信浏览器中
export function isWechatBrowser() {
  const ua = navigator.userAgent.toLowerCase()
  return /micromessenger/.test(ua)
}

// 检测是否在微信小程序中
export function isWechatMiniProgram() {
  const ua = navigator.userAgent.toLowerCase()
  return /miniprogram/.test(ua)
}

// 获取微信JS-SDK配置
export async function getWechatConfig(url = window.location.href) {
  try {
    // 这里应该调用后端接口获取微信配置
    // const response = await api.wechat.getConfig({ url })
    // return response.data
    
    // 临时返回模拟数据
    return {
      appId: 'your_app_id',
      timestamp: Math.floor(Date.now() / 1000),
      nonceStr: Math.random().toString(36).substr(2, 15),
      signature: 'mock_signature'
    }
  } catch (error) {
    console.error('获取微信配置失败:', error)
    throw error
  }
}

// 初始化微信JS-SDK
export async function initWechatSDK() {
  if (!isWechatBrowser()) {
    return false
  }
  
  try {
    const config = await getWechatConfig()
    
    return new Promise((resolve, reject) => {
      if (typeof wx === 'undefined') {
        // 动态加载微信JS-SDK
        const script = document.createElement('script')
        script.src = 'https://res.wx.qq.com/open/js/jweixin-1.6.0.js'
        script.onload = () => {
          configWechat(config, resolve, reject)
        }
        script.onerror = reject
        document.head.appendChild(script)
      } else {
        configWechat(config, resolve, reject)
      }
    })
  } catch (error) {
    console.error('初始化微信SDK失败:', error)
    return false
  }
}

// 配置微信
function configWechat(config, resolve, reject) {
  wx.config({
    debug: false,
    appId: config.appId,
    timestamp: config.timestamp,
    nonceStr: config.nonceStr,
    signature: config.signature,
    jsApiList: [
      'updateAppMessageShareData',
      'updateTimelineShareData',
      'onMenuShareAppMessage',
      'onMenuShareTimeline',
      'chooseImage',
      'uploadImage',
      'getLocation',
      'openLocation'
    ]
  })
  
  wx.ready(() => {
    console.log('微信SDK初始化成功')
    resolve(true)
  })
  
  wx.error((res) => {
    console.error('微信SDK初始化失败:', res)
    reject(res)
  })
}

// 配置微信分享
export function configWechatShare(shareData) {
  if (!isWechatBrowser() || typeof wx === 'undefined') {
    return
  }
  
  const defaultData = {
    title: '知识付费平台',
    desc: '优质学习资源分享平台',
    link: window.location.href,
    imgUrl: `${window.location.origin}/images/share-default.jpg`
  }
  
  const finalData = { ...defaultData, ...shareData }
  
  // 分享给朋友
  wx.updateAppMessageShareData({
    title: finalData.title,
    desc: finalData.desc,
    link: finalData.link,
    imgUrl: finalData.imgUrl,
    success: () => {
      console.log('分享给朋友配置成功')
    },
    fail: (error) => {
      console.error('分享给朋友配置失败:', error)
    }
  })
  
  // 分享到朋友圈
  wx.updateTimelineShareData({
    title: finalData.title,
    link: finalData.link,
    imgUrl: finalData.imgUrl,
    success: () => {
      console.log('分享到朋友圈配置成功')
    },
    fail: (error) => {
      console.error('分享到朋友圈配置失败:', error)
    }
  })
  
  // 兼容旧版本
  wx.onMenuShareAppMessage({
    title: finalData.title,
    desc: finalData.desc,
    link: finalData.link,
    imgUrl: finalData.imgUrl
  })
  
  wx.onMenuShareTimeline({
    title: finalData.title,
    link: finalData.link,
    imgUrl: finalData.imgUrl
  })
}

// 微信支付
export function wechatPay(payData) {
  return new Promise((resolve, reject) => {
    if (!isWechatBrowser() || typeof WeixinJSBridge === 'undefined') {
      reject(new Error('不支持微信支付'))
      return
    }
    
    WeixinJSBridge.invoke('getBrandWCPayRequest', {
      appId: payData.appId,
      timeStamp: payData.timeStamp,
      nonceStr: payData.nonceStr,
      package: payData.package,
      signType: payData.signType,
      paySign: payData.paySign
    }, (res) => {
      if (res.err_msg === 'get_brand_wcpay_request:ok') {
        resolve(res)
      } else {
        reject(new Error(res.err_msg || '支付失败'))
      }
    })
  })
}

// 获取微信用户信息
export function getWechatUserInfo() {
  return new Promise((resolve, reject) => {
    if (!isWechatBrowser()) {
      reject(new Error('不在微信环境中'))
      return
    }
    
    // 这里需要引导用户授权
    const appId = 'your_app_id'
    const redirectUri = encodeURIComponent(window.location.href)
    const scope = 'snsapi_userinfo'
    const state = Math.random().toString(36).substr(2, 15)
    
    const authUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appId}&redirect_uri=${redirectUri}&response_type=code&scope=${scope}&state=${state}#wechat_redirect`
    
    window.location.href = authUrl
  })
}

// 从URL中获取微信授权码
export function getWechatCodeFromUrl() {
  const urlParams = new URLSearchParams(window.location.search)
  return urlParams.get('code')
}

// 清除URL中的微信参数
export function clearWechatParams() {
  const url = new URL(window.location.href)
  url.searchParams.delete('code')
  url.searchParams.delete('state')
  
  if (url.search !== window.location.search) {
    window.history.replaceState({}, document.title, url.toString())
  }
}
