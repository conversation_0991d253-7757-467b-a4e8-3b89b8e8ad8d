<template>
  <div class="home-page">
    <!-- 顶部导航 -->
    <header class="header">
      <div class="container">
        <div class="header-content">
          <div class="logo">
            <h1>知识付费平台</h1>
          </div>
          <nav class="nav">
            <router-link to="/" class="nav-item active">首页</router-link>
            <router-link to="/category" class="nav-item">分类</router-link>
            <router-link to="/search" class="nav-item">搜索</router-link>
          </nav>
          <div class="user-actions">
            <template v-if="userStore.isLoggedIn">
              <el-dropdown @command="handleUserCommand">
                <span class="user-info">
                  <el-avatar :src="userStore.userInfo?.avatar" :size="32">
                    {{ userStore.userInfo?.nickname?.charAt(0) }}
                  </el-avatar>
                  <span class="username">{{ userStore.userInfo?.nickname }}</span>
                  <el-icon><ArrowDown /></el-icon>
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="profile">个人中心</el-dropdown-item>
                    <el-dropdown-item command="points">我的积分</el-dropdown-item>
                    <el-dropdown-item command="favorites">我的收藏</el-dropdown-item>
                    <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
            <template v-else>
              <el-button type="primary" @click="$router.push('/auth/login')">
                登录
              </el-button>
            </template>
          </div>
        </div>
      </div>
    </header>

    <!-- 主要内容 -->
    <main class="main">
      <!-- 轮播图 -->
      <section v-if="homeData.banners?.length" class="banner-section">
        <div class="container">
          <el-carousel height="300px" indicator-position="outside">
            <el-carousel-item v-for="(banner, index) in homeData.banners" :key="index">
              <div class="banner-item" @click="handleBannerClick(banner)">
                <img :src="banner.image" :alt="banner.title" />
                <div class="banner-overlay">
                  <h2>{{ banner.title }}</h2>
                </div>
              </div>
            </el-carousel-item>
          </el-carousel>
        </div>
      </section>

      <!-- 快捷功能 -->
      <section class="quick-actions">
        <div class="container">
          <div class="actions-grid">
            <div class="action-item" @click="handleQuickAction('sign')">
              <el-icon size="24"><Calendar /></el-icon>
              <span>每日签到</span>
            </div>
            <div class="action-item" @click="handleQuickAction('search')">
              <el-icon size="24"><Search /></el-icon>
              <span>搜索内容</span>
            </div>
            <div class="action-item" @click="handleQuickAction('category')">
              <el-icon size="24"><Grid /></el-icon>
              <span>分类浏览</span>
            </div>
            <div class="action-item" @click="handleQuickAction('invite')">
              <el-icon size="24"><Share /></el-icon>
              <span>邀请好友</span>
            </div>
          </div>
        </div>
      </section>

      <!-- 统计信息 -->
      <section v-if="stats" class="stats-section">
        <div class="container">
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-number">{{ formatNumber(stats.totalContents) }}</div>
              <div class="stat-label">总内容数</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ formatNumber(stats.totalUsers) }}</div>
              <div class="stat-label">用户数</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ formatNumber(stats.totalUnlocks) }}</div>
              <div class="stat-label">解锁次数</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">{{ formatNumber(stats.todayContents) }}</div>
              <div class="stat-label">今日新增</div>
            </div>
          </div>
        </div>
      </section>

      <!-- 分类导航 -->
      <section v-if="homeData.categories?.length" class="categories-section">
        <div class="container">
          <h2 class="section-title">热门分类</h2>
          <div class="categories-grid">
            <div 
              v-for="category in homeData.categories" 
              :key="category.id"
              class="category-item"
              @click="$router.push(`/category?categoryId=${category.id}`)"
            >
              <div class="category-icon">{{ category.icon }}</div>
              <div class="category-name">{{ category.name }}</div>
              <div class="category-count">{{ category.contentCount }}个内容</div>
            </div>
          </div>
        </div>
      </section>

      <!-- 热门内容 -->
      <section v-if="homeData.hotContents?.length" class="content-section">
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">🔥 热门内容</h2>
            <el-button text @click="$router.push('/category?sort=hot')">
              查看更多 <el-icon><ArrowRight /></el-icon>
            </el-button>
          </div>
          <div class="content-grid">
            <ContentCard 
              v-for="content in homeData.hotContents" 
              :key="content.id"
              :content="content"
              @click="$router.push(`/content/${content.id}`)"
            />
          </div>
        </div>
      </section>

      <!-- 推荐内容 -->
      <section v-if="homeData.recommendContents?.length" class="content-section">
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">⭐ 精选推荐</h2>
            <el-button text @click="$router.push('/category?sort=recommend')">
              查看更多 <el-icon><ArrowRight /></el-icon>
            </el-button>
          </div>
          <div class="content-grid">
            <ContentCard 
              v-for="content in homeData.recommendContents" 
              :key="content.id"
              :content="content"
              @click="$router.push(`/content/${content.id}`)"
            />
          </div>
        </div>
      </section>

      <!-- 最新内容 -->
      <section v-if="homeData.latestContents?.length" class="content-section">
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">🆕 最新发布</h2>
            <el-button text @click="$router.push('/category?sort=latest')">
              查看更多 <el-icon><ArrowRight /></el-icon>
            </el-button>
          </div>
          <div class="content-list">
            <ContentListItem 
              v-for="content in homeData.latestContents" 
              :key="content.id"
              :content="content"
              @click="$router.push(`/content/${content.id}`)"
            />
          </div>
        </div>
      </section>
    </main>

    <!-- 底部 -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-links">
            <router-link to="/about">关于我们</router-link>
            <router-link to="/privacy">隐私政策</router-link>
            <router-link to="/terms">服务条款</router-link>
          </div>
          <div class="footer-copyright">
            © 2023 知识付费平台. All rights reserved.
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useSystemStore } from '@/stores/system'
import { formatNumber } from '@/utils'
import { ElMessage } from 'element-plus'
import {
  ArrowDown,
  ArrowRight,
  Calendar,
  Search,
  Grid,
  Share,
  View,
  Star,
  Clock,
  Unlock
} from '@element-plus/icons-vue'
import ContentCard from '@/components/ContentCard.vue'
import ContentListItem from '@/components/ContentListItem.vue'

const router = useRouter()
const userStore = useUserStore()
const systemStore = useSystemStore()

const homeData = ref({})
const stats = ref(null)
const loading = ref(false)

onMounted(() => {
  loadHomeData()
})

// 加载首页数据
async function loadHomeData() {
  try {
    loading.value = true
    
    const [homeResult, statsResult] = await Promise.all([
      systemStore.getHomeData(),
      systemStore.getStats()
    ])
    
    homeData.value = homeResult
    stats.value = statsResult
  } catch (error) {
    console.error('加载首页数据失败:', error)
    ElMessage.error('加载失败，请刷新重试')
  } finally {
    loading.value = false
  }
}

// 处理轮播图点击
function handleBannerClick(banner) {
  if (banner.link) {
    if (banner.link.startsWith('/')) {
      router.push(banner.link)
    } else {
      window.open(banner.link, '_blank')
    }
  }
}

// 处理快捷操作
function handleQuickAction(action) {
  switch (action) {
    case 'sign':
      if (userStore.isLoggedIn) {
        router.push('/point/sign')
      } else {
        router.push('/auth/login')
      }
      break
    case 'search':
      router.push('/search')
      break
    case 'category':
      router.push('/category')
      break
    case 'invite':
      if (userStore.isLoggedIn) {
        router.push('/user/invite')
      } else {
        router.push('/auth/login')
      }
      break
  }
}

// 处理用户下拉菜单
function handleUserCommand(command) {
  switch (command) {
    case 'profile':
      router.push('/user')
      break
    case 'points':
      router.push('/user/points')
      break
    case 'favorites':
      router.push('/user/favorites')
      break
    case 'logout':
      userStore.logout()
      ElMessage.success('已退出登录')
      break
  }
}
</script>

<style lang="scss" scoped>
.home-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
  
  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 64px;
  }
  
  .logo h1 {
    margin: 0;
    color: #1890ff;
    font-size: 20px;
  }
  
  .nav {
    display: flex;
    gap: 32px;
    
    .nav-item {
      color: #666;
      font-weight: 500;
      transition: color 0.3s;
      
      &:hover,
      &.active {
        color: #1890ff;
      }
    }
  }
  
  .user-info {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    
    .username {
      font-weight: 500;
    }
  }
}

.main {
  flex: 1;
}

.banner-section {
  margin-bottom: 40px;
  
  .banner-item {
    position: relative;
    height: 300px;
    cursor: pointer;
    overflow: hidden;
    border-radius: 8px;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .banner-overlay {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
      color: white;
      padding: 40px 20px 20px;
      
      h2 {
        margin: 0;
        font-size: 24px;
      }
    }
  }
}

.quick-actions {
  margin-bottom: 40px;
  
  .actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 16px;
    max-width: 600px;
    margin: 0 auto;
  }
  
  .action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }
    
    span {
      font-size: 14px;
      color: #666;
    }
  }
}

.stats-section {
  margin-bottom: 40px;
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
  }
  
  .stat-item {
    text-align: center;
    padding: 24px;
    background: #fff;
    border-radius: 8px;
    
    .stat-number {
      font-size: 32px;
      font-weight: bold;
      color: #1890ff;
      margin-bottom: 8px;
    }
    
    .stat-label {
      color: #666;
      font-size: 14px;
    }
  }
}

.categories-section {
  margin-bottom: 40px;
  
  .categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 16px;
  }
  
  .category-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }
    
    .category-icon {
      font-size: 32px;
    }
    
    .category-name {
      font-weight: 500;
      color: #333;
    }
    
    .category-count {
      font-size: 12px;
      color: #999;
    }
  }
}

.content-section {
  margin-bottom: 40px;
  
  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
  }
  
  .section-title {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #333;
  }
  
  .content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
  }
  
  .content-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
}

.footer {
  background: #333;
  color: #fff;
  padding: 40px 0;
  margin-top: auto;
  
  .footer-content {
    text-align: center;
  }
  
  .footer-links {
    display: flex;
    justify-content: center;
    gap: 24px;
    margin-bottom: 16px;
    
    a {
      color: #ccc;
      transition: color 0.3s;
      
      &:hover {
        color: #fff;
      }
    }
  }
  
  .footer-copyright {
    color: #999;
    font-size: 14px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .header {
    .header-content {
      height: 56px;
    }
    
    .nav {
      display: none;
    }
    
    .logo h1 {
      font-size: 18px;
    }
  }
  
  .banner-section {
    margin-bottom: 24px;
    
    .banner-item {
      height: 200px;
      
      .banner-overlay h2 {
        font-size: 18px;
      }
    }
  }
  
  .quick-actions {
    .actions-grid {
      grid-template-columns: repeat(4, 1fr);
      gap: 12px;
    }
    
    .action-item {
      padding: 16px 8px;
      
      span {
        font-size: 12px;
      }
    }
  }
  
  .stats-section {
    .stats-grid {
      grid-template-columns: repeat(2, 1fr);
    }
    
    .stat-item {
      padding: 16px;
      
      .stat-number {
        font-size: 24px;
      }
    }
  }
  
  .categories-section {
    .categories-grid {
      grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }
    
    .category-item {
      padding: 16px 8px;
      
      .category-icon {
        font-size: 24px;
      }
      
      .category-name {
        font-size: 14px;
      }
    }
  }
  
  .content-section {
    .content-grid {
      grid-template-columns: 1fr;
    }
    
    .section-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }
  }
  
  .footer {
    padding: 24px 0;
    
    .footer-links {
      flex-direction: column;
      gap: 12px;
    }
  }
}
</style>
