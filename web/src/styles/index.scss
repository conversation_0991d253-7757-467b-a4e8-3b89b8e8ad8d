// 重置样式
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  background-color: #f5f5f5;
}

// 清除默认样式
ul, ol {
  margin: 0;
  padding: 0;
  list-style: none;
}

a {
  color: inherit;
  text-decoration: none;
  
  &:hover {
    text-decoration: none;
  }
}

img {
  max-width: 100%;
  height: auto;
  vertical-align: middle;
}

// 布局相关
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.flex {
  display: flex;
  
  &.center {
    align-items: center;
    justify-content: center;
  }
  
  &.between {
    justify-content: space-between;
  }
  
  &.around {
    justify-content: space-around;
  }
  
  &.column {
    flex-direction: column;
  }
  
  &.wrap {
    flex-wrap: wrap;
  }
}

// 文本相关
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: #1890ff; }
.text-success { color: #52c41a; }
.text-warning { color: #faad14; }
.text-danger { color: #ff4d4f; }
.text-info { color: #13c2c2; }
.text-muted { color: #999; }

.font-bold { font-weight: bold; }
.font-normal { font-weight: normal; }

// 间距相关
.m-0 { margin: 0; }
.mt-0 { margin-top: 0; }
.mr-0 { margin-right: 0; }
.mb-0 { margin-bottom: 0; }
.ml-0 { margin-left: 0; }

.p-0 { padding: 0; }
.pt-0 { padding-top: 0; }
.pr-0 { padding-right: 0; }
.pb-0 { padding-bottom: 0; }
.pl-0 { padding-left: 0; }

@for $i from 1 through 5 {
  .m-#{$i} { margin: #{$i * 8}px; }
  .mt-#{$i} { margin-top: #{$i * 8}px; }
  .mr-#{$i} { margin-right: #{$i * 8}px; }
  .mb-#{$i} { margin-bottom: #{$i * 8}px; }
  .ml-#{$i} { margin-left: #{$i * 8}px; }
  
  .p-#{$i} { padding: #{$i * 8}px; }
  .pt-#{$i} { padding-top: #{$i * 8}px; }
  .pr-#{$i} { padding-right: #{$i * 8}px; }
  .pb-#{$i} { padding-bottom: #{$i * 8}px; }
  .pl-#{$i} { padding-left: #{$i * 8}px; }
}

// 显示/隐藏
.hidden { display: none; }
.visible { visibility: visible; }
.invisible { visibility: hidden; }

// 响应式显示
@media (max-width: 768px) {
  .hidden-mobile { display: none !important; }
}

@media (min-width: 769px) {
  .hidden-desktop { display: none !important; }
}

// 卡片样式
.card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  
  &.hover {
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      transform: translateY(-2px);
    }
  }
}

// 按钮样式
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  &.primary {
    background: #1890ff;
    color: #fff;
    
    &:hover:not(:disabled) {
      background: #40a9ff;
    }
  }
  
  &.success {
    background: #52c41a;
    color: #fff;
    
    &:hover:not(:disabled) {
      background: #73d13d;
    }
  }
  
  &.warning {
    background: #faad14;
    color: #fff;
    
    &:hover:not(:disabled) {
      background: #ffc53d;
    }
  }
  
  &.danger {
    background: #ff4d4f;
    color: #fff;
    
    &:hover:not(:disabled) {
      background: #ff7875;
    }
  }
  
  &.ghost {
    background: transparent;
    border: 1px solid #d9d9d9;
    color: #333;
    
    &:hover:not(:disabled) {
      border-color: #1890ff;
      color: #1890ff;
    }
  }
  
  &.small {
    padding: 4px 8px;
    font-size: 12px;
  }
  
  &.large {
    padding: 12px 24px;
    font-size: 16px;
  }
  
  &.block {
    width: 100%;
  }
  
  &.round {
    border-radius: 20px;
  }
}

// 标签样式
.tag {
  display: inline-block;
  padding: 2px 8px;
  font-size: 12px;
  border-radius: 4px;
  background: #f0f0f0;
  color: #666;
  
  &.primary { background: #e6f7ff; color: #1890ff; }
  &.success { background: #f6ffed; color: #52c41a; }
  &.warning { background: #fffbe6; color: #faad14; }
  &.danger { background: #fff2f0; color: #ff4d4f; }
}

// 加载动画
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
  
  &:hover {
    background: #a8a8a8;
  }
}

// 文本省略
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ellipsis-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.ellipsis-3 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    padding: 0 12px;
  }
  
  body {
    font-size: 13px;
  }
  
  .btn {
    padding: 10px 16px;
    font-size: 14px;
    
    &.small {
      padding: 6px 12px;
      font-size: 12px;
    }
    
    &.large {
      padding: 14px 20px;
      font-size: 16px;
    }
  }
}

// 暗色主题支持
@media (prefers-color-scheme: dark) {
  body {
    background-color: #1a1a1a;
    color: #e6e6e6;
  }
  
  .card {
    background: #2a2a2a;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
}
