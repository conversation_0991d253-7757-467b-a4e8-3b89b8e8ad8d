<template>
  <div class="content-list-item" @click="$emit('click')">
    <div class="item-cover">
      <img :src="content.coverImage" :alt="content.title" />
      <div class="unlock-badge">
        <span v-if="content.unlockType === 1" class="price-badge">
          {{ content.unlockPrice }}积分
        </span>
        <span v-else-if="content.unlockType === 2" class="vip-badge">
          VIP
        </span>
        <span v-else-if="content.unlockType === 4" class="free-badge">
          免费
        </span>
      </div>
    </div>
    <div class="item-content">
      <h3 class="item-title">{{ content.title }}</h3>
      <p v-if="content.summary" class="item-summary">{{ content.summary }}</p>
      <div class="item-meta">
        <span class="meta-item">
          <el-icon><View /></el-icon>
          {{ formatNumber(content.viewCount) }}
        </span>
        <span class="meta-item">
          <el-icon><Star /></el-icon>
          {{ formatNumber(content.likeCount) }}
        </span>
        <span v-if="content.createdAt" class="meta-item">
          <el-icon><Clock /></el-icon>
          {{ fromNow(content.createdAt) }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { formatNumber, fromNow } from '@/utils'
import { View, Star, Clock } from '@element-plus/icons-vue'

defineProps({
  content: {
    type: Object,
    required: true
  }
})

defineEmits(['click'])
</script>

<style lang="scss" scoped>
.content-list-item {
  display: flex;
  gap: 16px;
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }
}

.item-cover {
  position: relative;
  flex-shrink: 0;
  width: 120px;
  height: 80px;
  border-radius: 6px;
  overflow: hidden;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.unlock-badge {
  position: absolute;
  top: 4px;
  right: 4px;
  
  .price-badge {
    background: rgba(255, 77, 79, 0.9);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: 500;
  }
  
  .vip-badge {
    background: rgba(250, 173, 20, 0.9);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: 500;
  }
  
  .free-badge {
    background: rgba(82, 196, 26, 0.9);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: 500;
  }
}

.item-content {
  flex: 1;
  min-width: 0;
}

.item-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.item-summary {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.item-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 12px;
  color: #999;
  
  .meta-item {
    display: flex;
    align-items: center;
    gap: 4px;
  }
}

@media (max-width: 768px) {
  .content-list-item {
    gap: 12px;
    padding: 12px;
  }
  
  .item-cover {
    width: 80px;
    height: 60px;
  }
  
  .item-title {
    font-size: 14px;
  }
  
  .item-summary {
    font-size: 13px;
  }
  
  .item-meta {
    gap: 12px;
    
    .meta-item {
      font-size: 11px;
    }
  }
}
</style>
