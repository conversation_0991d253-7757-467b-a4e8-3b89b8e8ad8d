<template>
  <div class="content-card" @click="$emit('click')">
    <div class="card-cover">
      <img :src="content.coverImage" :alt="content.title" />
      <div class="card-overlay">
        <div class="unlock-info">
          <span v-if="content.unlockType === 1" class="price-tag">
            {{ content.unlockPrice }}积分
          </span>
          <span v-else-if="content.unlockType === 2" class="vip-tag">
            VIP专享
          </span>
          <span v-else-if="content.unlockType === 4" class="free-tag">
            免费
          </span>
        </div>
      </div>
    </div>
    <div class="card-content">
      <h3 class="card-title">{{ content.title }}</h3>
      <p v-if="content.summary" class="card-summary">{{ content.summary }}</p>
      <div class="card-meta">
        <span class="meta-item">
          <el-icon><View /></el-icon>
          {{ formatNumber(content.viewCount) }}
        </span>
        <span class="meta-item">
          <el-icon><Star /></el-icon>
          {{ formatNumber(content.likeCount) }}
        </span>
        <span v-if="content.unlockCount" class="meta-item">
          <el-icon><Unlock /></el-icon>
          {{ formatNumber(content.unlockCount) }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { formatNumber } from '@/utils'
import { View, Star, Unlock } from '@element-plus/icons-vue'

defineProps({
  content: {
    type: Object,
    required: true
  }
})

defineEmits(['click'])
</script>

<style lang="scss" scoped>
.content-card {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }
}

.card-cover {
  position: relative;
  height: 200px;
  overflow: hidden;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }
  
  &:hover img {
    transform: scale(1.05);
  }
}

.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent 60%, rgba(0, 0, 0, 0.7));
  display: flex;
  align-items: flex-end;
  padding: 16px;
}

.unlock-info {
  .price-tag {
    background: #ff4d4f;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
  }
  
  .vip-tag {
    background: linear-gradient(45deg, #faad14, #ffc53d);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
  }
  
  .free-tag {
    background: #52c41a;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
  }
}

.card-content {
  padding: 16px;
}

.card-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.card-summary {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.card-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 12px;
  color: #999;
  
  .meta-item {
    display: flex;
    align-items: center;
    gap: 4px;
  }
}

@media (max-width: 768px) {
  .card-cover {
    height: 160px;
  }
  
  .card-content {
    padding: 12px;
  }
  
  .card-title {
    font-size: 14px;
  }
  
  .card-summary {
    font-size: 13px;
  }
}
</style>
